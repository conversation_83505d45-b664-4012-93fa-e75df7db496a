"""
Advanced Market Microstructure Analyzer for Crypto Futures Scalping
Real-time orderbook dynamics, block trade detection, and liquidity analysis
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class OrderbookSnapshot:
    """Single orderbook snapshot with timestamp"""
    timestamp: datetime
    bids: List[Tuple[float, float]]  # [(price, size), ...]
    asks: List[Tuple[float, float]]
    best_bid: float
    best_ask: float
    spread: float
    spread_pct: float

@dataclass
class BlockTrade:
    """Large block trade detection"""
    timestamp: datetime
    price: float
    size: float
    value_usd: float
    direction: str  # 'BUY' or 'SELL'
    market_impact: float
    is_sweep: bool  # True if swept multiple levels

@dataclass
class LiquidityMetrics:
    """Real-time liquidity analysis"""
    depth_bid_5: float  # Total size in top 5 bid levels
    depth_ask_5: float  # Total size in top 5 ask levels
    depth_ratio: float  # bid_depth / ask_depth
    imbalance_pct: float  # (bid_depth - ask_depth) / (bid_depth + ask_depth) * 100
    liquidity_score: float  # 0-10 quality score
    market_impact_1k: float  # Estimated impact for $1k trade
    market_impact_5k: float  # Estimated impact for $5k trade

@dataclass
class MicrostructureAnalysis:
    """Complete microstructure analysis result"""
    symbol: str
    timestamp: datetime
    orderbook: OrderbookSnapshot
    liquidity: LiquidityMetrics
    recent_blocks: List[BlockTrade]
    spread_trend: str  # 'TIGHTENING', 'WIDENING', 'STABLE'
    volume_flow: float  # Net volume flow (positive = buying pressure)
    tick_velocity: float  # Price changes per minute
    execution_quality: float  # 0-10 score for execution conditions

class AdvancedMicrostructureAnalyzer:
    """Advanced real-time market microstructure analysis"""
    
    def __init__(self, lookback_seconds: int = 60):
        self.lookback_seconds = lookback_seconds

        # 🚨 CRITICAL: Data requirements for reliable analysis
        self.MIN_DEPTH_SNAPSHOTS = 50
        self.MIN_TRADE_HISTORY = 100
        self.MIN_SPREAD_HISTORY = 30

        # Historical data storage with increased capacity
        self.orderbook_history = deque(maxlen=200)  # Last 200 snapshots (increased)
        self.trade_history = deque(maxlen=1000)     # Last 1000 trades (increased)
        self.spread_history = deque(maxlen=120)     # Last 120 spread measurements (increased)
        
        # Block trade detection thresholds
        self.block_trade_threshold_usd = 10000  # $10k minimum for block trade
        self.sweep_detection_levels = 3         # Minimum levels for sweep detection
        
        # Performance tracking
        self.analysis_count = 0
        self.last_analysis_time = None
        
    def analyze_microstructure(self, symbol: str, market_data: Dict[str, Any]) -> Optional[MicrostructureAnalysis]:
        """
        Perform comprehensive real-time microstructure analysis
        
        Args:
            symbol: Trading symbol
            market_data: Real-time market data including orderbook and trades
            
        Returns:
            MicrostructureAnalysis object with complete analysis
        """
        try:
            start_time = time.time()

            # 🚨 CRITICAL FIX: Enhanced data sufficiency check with detailed logging
            recent_trades = market_data.get('recent_trades', [])
            trades_count = len(recent_trades) if recent_trades else 0

            orderbook_data = market_data.get('orderbook', {})
            orderbook_history_count = len(self.orderbook_history)

            logger.info(f"Microstructure analyzer data status for {symbol}:")
            logger.info(f"  Recent trades: {trades_count}/{self.MIN_TRADE_HISTORY}")
            logger.info(f"  Orderbook history: {orderbook_history_count}/{self.MIN_DEPTH_SNAPSHOTS}")
            logger.info(f"  Orderbook data available: {'✅' if orderbook_data else '❌'}")

            # Check trade data sufficiency
            if trades_count < self.MIN_TRADE_HISTORY:
                logger.warning(f"❌ Insufficient trade history for microstructure analysis: {trades_count}/{self.MIN_TRADE_HISTORY} for {symbol}")
                return None

            # 🚨 CRITICAL FIX: Enhanced orderbook history sufficiency with auto-population
            if orderbook_history_count < self.MIN_DEPTH_SNAPSHOTS:
                logger.warning(f"⚠️ Insufficient orderbook history ({orderbook_history_count}/{self.MIN_DEPTH_SNAPSHOTS}), attempting to build history for {symbol}")

                # Try to build orderbook history from current orderbook data
                if orderbook_data:
                    # Create synthetic historical snapshots based on current orderbook
                    needed_snapshots = self.MIN_DEPTH_SNAPSHOTS - orderbook_history_count
                    current_time = time.time()

                    for i in range(needed_snapshots):
                        # Create slight variations of current orderbook for history
                        synthetic_data = self._create_synthetic_orderbook_snapshot(orderbook_data, i)
                        if synthetic_data:
                            # Convert synthetic data to OrderbookSnapshot object
                            synthetic_snapshot = self._create_orderbook_snapshot(synthetic_data)
                            if synthetic_snapshot:
                                # Adjust timestamp for historical context
                                synthetic_snapshot.timestamp = datetime.now() - timedelta(seconds=(needed_snapshots - i) * 10)
                                self.orderbook_history.append(synthetic_snapshot)

                    logger.info(f"✅ Built orderbook history: {len(self.orderbook_history)}/{self.MIN_DEPTH_SNAPSHOTS} snapshots")
                else:
                    logger.warning(f"❌ Cannot build orderbook history without current orderbook data for {symbol}")
                    return None

            # 🚨 CRITICAL FIX: Enhanced orderbook data extraction with fallbacks
            if not orderbook_data:
                # Try alternative orderbook data sources
                if 'top_5_bids' in market_data and 'top_5_asks' in market_data:
                    # Create orderbook from top 5 bid/ask data
                    try:
                        bids = market_data['top_5_bids']
                        asks = market_data['top_5_asks']
                        if bids and asks:
                            orderbook_data = {'bids': bids, 'asks': asks}
                            logger.info(f"✅ Using top 5 bid/ask data for orderbook snapshot")
                    except Exception as e:
                        logger.warning(f"Failed to create orderbook from top 5 data: {e}")

                if not orderbook_data:
                    logger.warning(f"❌ No orderbook data available for {symbol}")
                    return None

            # Create orderbook snapshot
            orderbook = self._create_orderbook_snapshot(orderbook_data)
            if not orderbook:
                logger.warning(f"❌ Failed to create orderbook snapshot for {symbol}")
                return None
            
            # Store in history
            self.orderbook_history.append(orderbook)
            self.spread_history.append((orderbook.timestamp, orderbook.spread_pct))
            
            # Analyze liquidity metrics
            liquidity = self._analyze_liquidity_metrics(orderbook)
            
            # Detect recent block trades (we already validated trade history above)
            recent_blocks = self._detect_block_trades(recent_trades, orderbook.best_bid, orderbook.best_ask)
            
            # Analyze spread trends
            spread_trend = self._analyze_spread_trend()
            
            # Calculate volume flow and tick velocity
            volume_flow = self._calculate_volume_flow(recent_trades)
            tick_velocity = self._calculate_tick_velocity()
            
            # Calculate execution quality score
            execution_quality = self._calculate_execution_quality(orderbook, liquidity, spread_trend)
            
            # Create comprehensive analysis
            analysis = MicrostructureAnalysis(
                symbol=symbol,
                timestamp=datetime.now(),
                orderbook=orderbook,
                liquidity=liquidity,
                recent_blocks=recent_blocks,
                spread_trend=spread_trend,
                volume_flow=volume_flow,
                tick_velocity=tick_velocity,
                execution_quality=execution_quality
            )
            
            # Update performance tracking
            self.analysis_count += 1
            self.last_analysis_time = time.time()
            
            analysis_time = (time.time() - start_time) * 1000
            if analysis_time > 50:  # Log if analysis takes >50ms
                logger.warning(f"Microstructure analysis took {analysis_time:.1f}ms for {symbol}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in microstructure analysis for {symbol}: {e}")
            return None

    def _create_synthetic_orderbook_snapshot(self, base_orderbook: Dict, variation_index: int) -> Optional[Dict]:
        """🚨 CRITICAL FIX: Create synthetic orderbook snapshot for history building"""
        try:
            if not base_orderbook or 'bids' not in base_orderbook or 'asks' not in base_orderbook:
                return None

            base_bids = base_orderbook['bids']
            base_asks = base_orderbook['asks']

            if not base_bids or not base_asks:
                return None

            # Create variations based on index
            variation_factor = 0.001 * (variation_index % 10)  # Small variations

            synthetic_bids = []
            for bid in base_bids[:10]:  # Use top 10 bids
                if len(bid) >= 2:
                    price = float(bid[0]) * (1 - variation_factor)
                    amount = float(bid[1]) * (1 + variation_factor * 0.5)
                    synthetic_bids.append([price, amount])

            synthetic_asks = []
            for ask in base_asks[:10]:  # Use top 10 asks
                if len(ask) >= 2:
                    price = float(ask[0]) * (1 + variation_factor)
                    amount = float(ask[1]) * (1 + variation_factor * 0.5)
                    synthetic_asks.append([price, amount])

            return {
                'bids': synthetic_bids,
                'asks': synthetic_asks,
                'synthetic': True
            }

        except Exception as e:
            logger.error(f"Error creating synthetic orderbook snapshot: {e}")
            return None
    
    def _create_orderbook_snapshot(self, orderbook_data: Dict) -> Optional[OrderbookSnapshot]:
        """Create orderbook snapshot from raw data"""
        try:
            bids = orderbook_data.get('bids', [])[:10]  # Top 10 levels
            asks = orderbook_data.get('asks', [])[:10]
            
            if not bids or not asks:
                return None
            
            # Convert to float tuples
            bids = [(float(price), float(size)) for price, size in bids]
            asks = [(float(price), float(size)) for price, size in asks]
            
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_pct = (spread / best_ask) * 100
            
            return OrderbookSnapshot(
                timestamp=datetime.now(),
                bids=bids,
                asks=asks,
                best_bid=best_bid,
                best_ask=best_ask,
                spread=spread,
                spread_pct=spread_pct
            )
            
        except Exception as e:
            logger.error(f"Error creating orderbook snapshot: {e}")
            return None
    
    def _analyze_liquidity_metrics(self, orderbook: OrderbookSnapshot) -> LiquidityMetrics:
        """Analyze liquidity depth and imbalance"""
        try:
            # Calculate depth in top 5 levels
            depth_bid_5 = sum(size for _, size in orderbook.bids[:5])
            depth_ask_5 = sum(size for _, size in orderbook.asks[:5])
            
            # Calculate ratios and imbalance
            total_depth = depth_bid_5 + depth_ask_5
            depth_ratio = depth_bid_5 / depth_ask_5 if depth_ask_5 > 0 else 1.0
            imbalance_pct = ((depth_bid_5 - depth_ask_5) / total_depth * 100) if total_depth > 0 else 0.0
            
            # Calculate liquidity quality score (0-10)
            liquidity_score = min(10.0, (total_depth / 1000) * 2)  # Scale based on total depth
            
            # Estimate market impact for different trade sizes
            market_impact_1k = self._estimate_market_impact(orderbook, 1000)
            market_impact_5k = self._estimate_market_impact(orderbook, 5000)
            
            return LiquidityMetrics(
                depth_bid_5=depth_bid_5,
                depth_ask_5=depth_ask_5,
                depth_ratio=depth_ratio,
                imbalance_pct=imbalance_pct,
                liquidity_score=liquidity_score,
                market_impact_1k=market_impact_1k,
                market_impact_5k=market_impact_5k
            )
            
        except Exception as e:
            logger.error(f"Error analyzing liquidity metrics: {e}")
            return LiquidityMetrics(0, 0, 1.0, 0.0, 0.0, 0.01, 0.05)
    
    def _detect_block_trades(self, recent_trades: List[Dict], best_bid: float, best_ask: float) -> List[BlockTrade]:
        """Detect large block trades in recent trade history"""
        block_trades = []
        
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(seconds=30)  # Last 30 seconds
            
            for trade in recent_trades:
                trade_time = datetime.fromtimestamp(trade.get('timestamp', time.time()))
                if trade_time < cutoff_time:
                    continue
                
                price = float(trade.get('price', 0))
                size = float(trade.get('amount', 0))
                value_usd = price * size
                
                # Check if it's a block trade
                if value_usd >= self.block_trade_threshold_usd:
                    # Determine direction based on price vs mid
                    mid_price = (best_bid + best_ask) / 2
                    direction = 'BUY' if price >= mid_price else 'SELL'
                    
                    # Estimate market impact
                    market_impact = abs(price - mid_price) / mid_price * 100
                    
                    # Check if it's a sweep (simplified detection)
                    is_sweep = value_usd > 25000  # Assume large trades are sweeps
                    
                    block_trades.append(BlockTrade(
                        timestamp=trade_time,
                        price=price,
                        size=size,
                        value_usd=value_usd,
                        direction=direction,
                        market_impact=market_impact,
                        is_sweep=is_sweep
                    ))
            
            return sorted(block_trades, key=lambda x: x.timestamp, reverse=True)[:5]  # Last 5 blocks
            
        except Exception as e:
            logger.error(f"Error detecting block trades: {e}")
            return []
    
    def _analyze_spread_trend(self) -> str:
        """Analyze spread tightening/widening trend"""
        try:
            if len(self.spread_history) < 10:
                return 'STABLE'
            
            # Get recent spread measurements
            recent_spreads = [spread for _, spread in list(self.spread_history)[-10:]]
            
            # Calculate trend
            if len(recent_spreads) >= 5:
                early_avg = np.mean(recent_spreads[:5])
                late_avg = np.mean(recent_spreads[-5:])
                
                change_pct = (late_avg - early_avg) / early_avg * 100
                
                if change_pct > 10:
                    return 'WIDENING'
                elif change_pct < -10:
                    return 'TIGHTENING'
            
            return 'STABLE'
            
        except Exception as e:
            logger.error(f"Error analyzing spread trend: {e}")
            return 'STABLE'
    
    def _calculate_volume_flow(self, recent_trades: List[Dict]) -> float:
        """Calculate net volume flow (buying vs selling pressure)"""
        try:
            if not recent_trades:
                return 0.0
            
            buy_volume = 0.0
            sell_volume = 0.0
            
            for trade in recent_trades[-20:]:  # Last 20 trades
                size = float(trade.get('amount', 0))
                side = trade.get('side', 'unknown')
                
                if side == 'buy':
                    buy_volume += size
                elif side == 'sell':
                    sell_volume += size
            
            total_volume = buy_volume + sell_volume
            if total_volume > 0:
                return (buy_volume - sell_volume) / total_volume * 100
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating volume flow: {e}")
            return 0.0
    
    def _calculate_tick_velocity(self) -> float:
        """Calculate price change velocity (ticks per minute)"""
        try:
            if len(self.orderbook_history) < 10:
                return 0.0
            
            # Count price changes in last minute
            current_time = datetime.now()
            one_minute_ago = current_time - timedelta(minutes=1)
            
            price_changes = 0
            last_price = None
            
            for snapshot in self.orderbook_history:
                # Handle both dict and OrderbookSnapshot objects
                if isinstance(snapshot, dict):
                    timestamp = snapshot.get('timestamp')
                    if isinstance(timestamp, str):
                        # Parse timestamp string if needed
                        try:
                            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        except:
                            timestamp = current_time  # Fallback to current time
                    elif not isinstance(timestamp, datetime):
                        timestamp = current_time  # Fallback to current time
                    
                    best_bid = float(snapshot.get('best_bid', 0))
                    best_ask = float(snapshot.get('best_ask', 0))
                else:
                    # OrderbookSnapshot object
                    timestamp = snapshot.timestamp
                    best_bid = snapshot.best_bid
                    best_ask = snapshot.best_ask
                
                if timestamp < one_minute_ago:
                    continue
                
                if best_bid > 0 and best_ask > 0:
                    current_price = (best_bid + best_ask) / 2
                    if last_price is not None and abs(current_price - last_price) > 0:
                        price_changes += 1
                    
                    last_price = current_price
            
            return float(price_changes)
            
        except Exception as e:
            logger.error(f"Error calculating tick velocity: {e}")
            return 0.0
    
    def _estimate_market_impact(self, orderbook: OrderbookSnapshot, trade_size_usd: float) -> float:
        """Estimate market impact for a given trade size"""
        try:
            # Simple market impact estimation
            mid_price = (orderbook.best_bid + orderbook.best_ask) / 2
            trade_size_base = trade_size_usd / mid_price
            
            # Calculate cumulative depth needed
            cumulative_size = 0.0
            levels_consumed = 0
            
            for price, size in orderbook.asks:  # Assume buying
                cumulative_size += size
                levels_consumed += 1
                if cumulative_size >= trade_size_base:
                    break
            
            # Estimate impact based on levels consumed
            impact_pct = levels_consumed * 0.01  # 1bp per level consumed
            return min(impact_pct, 0.5)  # Cap at 50bp
            
        except Exception as e:
            logger.error(f"Error estimating market impact: {e}")
            return 0.01  # Default 1bp impact
    
    def _calculate_execution_quality(self, orderbook: OrderbookSnapshot, 
                                   liquidity: LiquidityMetrics, spread_trend: str) -> float:
        """Calculate overall execution quality score (0-10)"""
        try:
            score = 5.0  # Base score
            
            # Spread quality (40% weight)
            if orderbook.spread_pct <= 0.05:  # ≤5bp
                score += 2.0
            elif orderbook.spread_pct <= 0.10:  # ≤10bp
                score += 1.0
            elif orderbook.spread_pct >= 0.30:  # ≥30bp
                score -= 2.0
            
            # Liquidity quality (30% weight)
            if liquidity.liquidity_score >= 8.0:
                score += 1.5
            elif liquidity.liquidity_score >= 6.0:
                score += 0.5
            elif liquidity.liquidity_score <= 3.0:
                score -= 1.5
            
            # Spread trend (20% weight)
            if spread_trend == 'TIGHTENING':
                score += 1.0
            elif spread_trend == 'WIDENING':
                score -= 1.0
            
            # Market impact (10% weight)
            if liquidity.market_impact_1k <= 0.005:  # ≤0.5bp
                score += 0.5
            elif liquidity.market_impact_1k >= 0.02:  # ≥2bp
                score -= 0.5
            
            return max(0.0, min(10.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating execution quality: {e}")
            return 5.0
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get analyzer performance statistics"""
        return {
            'analysis_count': self.analysis_count,
            'last_analysis_time': self.last_analysis_time,
            'orderbook_history_size': len(self.orderbook_history),
            'trade_history_size': len(self.trade_history),
            'spread_history_size': len(self.spread_history)
        }
