"""
Data Strategy Manager for Optimal WebSocket vs REST API Usage
Manages efficient data sourcing strategy for different data types and requirements
"""

import time
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DataSource(Enum):
    """Data source types"""
    WEBSOCKET = "WEBSOCKET"
    REST_API = "REST_API"
    CACHE = "CACHE"
    HYBRID = "HYBRID"

@dataclass
class DataRequest:
    """Data request specification"""
    symbol: str
    data_type: str  # 'candles', 'trades', 'orderbook', 'ticker'
    timeframe: Optional[str] = None
    limit: int = 100
    max_age_seconds: int = 30
    priority: str = "NORMAL"  # HIGH, NORMAL, LOW

@dataclass
class DataStrategy:
    """Data sourcing strategy"""
    primary_source: DataSource
    fallback_source: DataSource
    cache_duration: int
    refresh_threshold: int
    quality_threshold: float

class DataStrategyManager:
    """Manages optimal data sourcing strategies for different use cases"""
    
    def __init__(self):
        # Data source strategies for different data types
        self.strategies = {
            # Real-time data - WebSocket primary
            'ticker': DataStrategy(
                primary_source=DataSource.WEBSOCKET,
                fallback_source=DataSource.REST_API,
                cache_duration=5,      # 5 seconds
                refresh_threshold=3,   # Refresh if older than 3 seconds
                quality_threshold=0.9
            ),
            
            'orderbook': DataStrategy(
                primary_source=DataSource.WEBSOCKET,
                fallback_source=DataSource.REST_API,
                cache_duration=2,      # 2 seconds
                refresh_threshold=1,   # Refresh if older than 1 second
                quality_threshold=0.95
            ),
            
            'trades': DataStrategy(
                primary_source=DataSource.WEBSOCKET,
                fallback_source=DataSource.REST_API,
                cache_duration=10,     # 10 seconds
                refresh_threshold=5,   # Refresh if older than 5 seconds
                quality_threshold=0.8
            ),
            
            # Historical data - REST API primary
            'candles_historical': DataStrategy(
                primary_source=DataSource.REST_API,
                fallback_source=DataSource.CACHE,
                cache_duration=300,    # 5 minutes
                refresh_threshold=60,  # Refresh if older than 1 minute
                quality_threshold=0.7
            ),
            
            # Real-time candles - Hybrid approach
            'candles_realtime': DataStrategy(
                primary_source=DataSource.HYBRID,
                fallback_source=DataSource.REST_API,
                cache_duration=60,     # 1 minute
                refresh_threshold=30,  # Refresh if older than 30 seconds
                quality_threshold=0.8
            )
        }
        
        # Performance tracking
        self.request_stats = {}
        self.source_performance = {
            DataSource.WEBSOCKET: {'success': 0, 'failure': 0, 'avg_latency': 0},
            DataSource.REST_API: {'success': 0, 'failure': 0, 'avg_latency': 0},
            DataSource.CACHE: {'success': 0, 'failure': 0, 'avg_latency': 0}
        }
        
    def get_optimal_strategy(self, request: DataRequest) -> DataStrategy:
        """Get optimal data sourcing strategy for a request"""
        try:
            # Determine data type category
            if request.data_type == 'candles':
                # Determine if historical or real-time based on limit and timeframe
                if request.limit > 50 or request.priority == "HIGH":
                    strategy_key = 'candles_historical'
                else:
                    strategy_key = 'candles_realtime'
            else:
                strategy_key = request.data_type
            
            # Get base strategy
            base_strategy = self.strategies.get(strategy_key, self.strategies['candles_historical'])
            
            # Adjust strategy based on request priority
            if request.priority == "HIGH":
                # High priority requests prefer fresh data
                base_strategy.refresh_threshold = min(base_strategy.refresh_threshold, 10)
                base_strategy.quality_threshold = min(base_strategy.quality_threshold + 0.1, 1.0)
            elif request.priority == "LOW":
                # Low priority requests can use older cached data
                base_strategy.refresh_threshold = base_strategy.refresh_threshold * 2
                base_strategy.quality_threshold = max(base_strategy.quality_threshold - 0.1, 0.5)
            
            return base_strategy
            
        except Exception as e:
            logger.error(f"Error determining optimal strategy: {e}")
            return self.strategies['candles_historical']  # Safe fallback
    
    def should_use_websocket(self, request: DataRequest) -> bool:
        """Determine if WebSocket should be used for this request"""
        try:
            strategy = self.get_optimal_strategy(request)
            
            # Check WebSocket performance
            ws_performance = self.source_performance[DataSource.WEBSOCKET]
            ws_success_rate = ws_performance['success'] / max(1, ws_performance['success'] + ws_performance['failure'])
            
            # Use WebSocket if:
            # 1. Strategy recommends it
            # 2. WebSocket performance is good (>80% success rate)
            # 3. Request is for real-time data
            return (
                strategy.primary_source == DataSource.WEBSOCKET and
                ws_success_rate > 0.8 and
                request.data_type in ['ticker', 'orderbook', 'trades']
            )
            
        except Exception as e:
            logger.error(f"Error determining WebSocket usage: {e}")
            return False
    
    def should_fetch_historical(self, request: DataRequest) -> bool:
        """Determine if historical data should be fetched via REST API"""
        try:
            # Fetch historical data if:
            # 1. Large amount of data requested (>50 candles)
            # 2. Data type is candles
            # 3. High priority request
            return (
                request.data_type == 'candles' and
                (request.limit > 50 or request.priority == "HIGH")
            )
            
        except Exception as e:
            logger.error(f"Error determining historical fetch: {e}")
            return True  # Safe fallback
    
    def get_cache_key(self, request: DataRequest) -> str:
        """Generate cache key for a data request"""
        return f"{request.symbol}_{request.data_type}_{request.timeframe}_{request.limit}"
    
    def is_cache_valid(self, cache_timestamp: float, request: DataRequest) -> bool:
        """Check if cached data is still valid"""
        try:
            strategy = self.get_optimal_strategy(request)
            age = time.time() - cache_timestamp
            return age < strategy.refresh_threshold
            
        except Exception as e:
            logger.error(f"Error checking cache validity: {e}")
            return False
    
    def record_request_performance(self, source: DataSource, success: bool, latency: float):
        """Record performance metrics for a data source"""
        try:
            stats = self.source_performance[source]
            
            if success:
                stats['success'] += 1
            else:
                stats['failure'] += 1
            
            # Update average latency (exponential moving average)
            if stats['avg_latency'] == 0:
                stats['avg_latency'] = latency
            else:
                stats['avg_latency'] = 0.9 * stats['avg_latency'] + 0.1 * latency
                
        except Exception as e:
            logger.error(f"Error recording performance: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for all data sources"""
        try:
            stats = {}
            
            for source, perf in self.source_performance.items():
                total_requests = perf['success'] + perf['failure']
                success_rate = perf['success'] / max(1, total_requests)
                
                stats[source.value] = {
                    'total_requests': total_requests,
                    'success_rate': success_rate,
                    'average_latency_ms': perf['avg_latency'] * 1000,
                    'status': 'GOOD' if success_rate > 0.9 else 'POOR' if success_rate < 0.7 else 'FAIR'
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting performance stats: {e}")
            return {}
    
    def optimize_strategies(self):
        """Optimize strategies based on performance data"""
        try:
            # Get current performance
            ws_perf = self.source_performance[DataSource.WEBSOCKET]
            rest_perf = self.source_performance[DataSource.REST_API]
            
            ws_success_rate = ws_perf['success'] / max(1, ws_perf['success'] + ws_perf['failure'])
            rest_success_rate = rest_perf['success'] / max(1, rest_perf['success'] + rest_perf['failure'])
            
            # If WebSocket performance is poor, adjust strategies
            if ws_success_rate < 0.7:
                logger.warning("WebSocket performance poor, adjusting strategies to favor REST API")
                
                # Switch real-time strategies to REST API fallback
                for strategy_name in ['ticker', 'orderbook', 'trades']:
                    if strategy_name in self.strategies:
                        strategy = self.strategies[strategy_name]
                        strategy.primary_source = DataSource.REST_API
                        strategy.fallback_source = DataSource.CACHE
            
            # If REST API performance is poor, increase cache usage
            if rest_success_rate < 0.7:
                logger.warning("REST API performance poor, increasing cache duration")
                
                for strategy in self.strategies.values():
                    strategy.cache_duration *= 2  # Double cache duration
                    strategy.refresh_threshold *= 1.5  # More lenient refresh
            
            logger.info("Strategy optimization completed")
            
        except Exception as e:
            logger.error(f"Error optimizing strategies: {e}")
    
    def get_recommended_limits(self, timeframe: str) -> Dict[str, int]:
        """Get recommended data limits for different use cases"""
        base_limits = {
            "1m": {"min": 60, "optimal": 120, "max": 200},
            "5m": {"min": 48, "optimal": 96, "max": 150},
            "15m": {"min": 32, "optimal": 64, "max": 100},
            "1h": {"min": 24, "optimal": 48, "max": 72},
            "4h": {"min": 12, "optimal": 24, "max": 36},
            "1d": {"min": 7, "optimal": 14, "max": 30}
        }
        
        return base_limits.get(timeframe, {"min": 20, "optimal": 50, "max": 100})
    
    def validate_data_quality(self, data: List[Any], request: DataRequest) -> float:
        """Validate data quality and return quality score (0-1)"""
        try:
            if not data:
                return 0.0
            
            # Basic quality checks
            quality_score = 1.0
            
            # Check data completeness
            expected_count = request.limit
            actual_count = len(data)
            completeness = min(1.0, actual_count / expected_count)
            quality_score *= completeness
            
            # Check data freshness (for real-time data)
            if request.data_type in ['ticker', 'orderbook', 'trades']:
                if hasattr(data[-1], 'timestamp') or (isinstance(data[-1], dict) and 'timestamp' in data[-1]):
                    try:
                        if isinstance(data[-1], dict):
                            last_timestamp = data[-1]['timestamp']
                        else:
                            last_timestamp = data[-1].timestamp
                        
                        age = time.time() - last_timestamp
                        freshness = max(0.0, 1.0 - (age / 60))  # Degrade over 1 minute
                        quality_score *= freshness
                    except:
                        pass  # Skip freshness check if timestamp not available
            
            return quality_score
            
        except Exception as e:
            logger.error(f"Error validating data quality: {e}")
            return 0.5  # Neutral score on error
