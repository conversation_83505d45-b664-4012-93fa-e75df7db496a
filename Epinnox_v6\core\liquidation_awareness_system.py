#!/usr/bin/env python3
"""
Liquidation Awareness System
Ensures LLM and system components understand and monitor liquidation lines in real-time
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LiquidationAlert:
    """Liquidation alert information"""
    alert_type: str  # 'WARNING', 'DANGER', 'CRITICAL'
    distance_pct: float
    liquidation_price: float
    current_price: float
    position_side: str
    recommended_action: str
    urgency_level: int  # 1-10 scale
    message: str

@dataclass
class PositionRisk:
    """Position risk assessment"""
    symbol: str
    side: str
    entry_price: float
    current_price: float
    position_size: float
    leverage: float
    liquidation_price: float
    distance_to_liquidation_pct: float
    risk_level: str
    margin_ratio: float
    unrealized_pnl: float

class LiquidationAwarenessSystem:
    """Real-time liquidation monitoring and awareness system"""
    
    def __init__(self, account_balance: float = 19.51):
        """
        Initialize liquidation awareness system
        
        Args:
            account_balance: Current account balance
        """
        self.account_balance = account_balance
        
        # 🚨 LIQUIDATION THRESHOLDS: Critical distance levels
        self.liquidation_thresholds = {
            'safe': 30.0,        # >30% from liquidation = safe
            'warning': 20.0,     # 20-30% from liquidation = warning
            'danger': 10.0,      # 10-20% from liquidation = danger
            'critical': 5.0      # <10% from liquidation = critical
        }
        
        # 🧠 LLM AWARENESS PROMPTS: Liquidation-aware decision making
        self.llm_awareness_prompts = {
            'position_analysis': """
🚨 LIQUIDATION AWARENESS - CRITICAL RISK ASSESSMENT

Current Position Risk Status:
- Distance to Liquidation: {distance_pct:.1f}%
- Liquidation Price: ${liquidation_price:.8f}
- Current Price: ${current_price:.8f}
- Risk Level: {risk_level}
- Margin Ratio: {margin_ratio:.1f}%

LIQUIDATION ZONES:
🟢 SAFE ZONE (>30%): Normal trading operations
🟡 WARNING ZONE (20-30%): Reduce position size, monitor closely
🟠 DANGER ZONE (10-20%): Consider partial close, no new positions
🔴 CRITICAL ZONE (<10%): IMMEDIATE ACTION REQUIRED

CURRENT STATUS: {current_status}
RECOMMENDED ACTION: {recommended_action}
""",
            
            'risk_management': """
🛡️ INTELLIGENT LIQUIDATION RISK MANAGEMENT

Account Protection Priority:
- Account Balance: ${account_balance:.2f}
- Total Exposure: ${total_exposure:.2f}
- Available Margin: ${available_margin:.2f}
- Liquidation Buffer: {liquidation_buffer:.1f}%

RISK MANAGEMENT RULES:
1. NEVER risk more than 5% of account per trade
2. Maintain minimum 15% buffer from liquidation
3. Close positions if margin ratio drops below 20%
4. Use stop losses to prevent liquidation events
5. Monitor real-time liquidation distance

LIQUIDATION PREVENTION STRATEGY:
- Max Position Size: {max_safe_position:.2f} USDT
- Recommended Leverage: {recommended_leverage:.1f}x
- Stop Loss Level: {stop_loss_level:.1f}%
""",
            
            'emergency_protocol': """
🚨 EMERGENCY LIQUIDATION PROTOCOL ACTIVATED

IMMEDIATE ACTIONS REQUIRED:
1. STOP all new position entries
2. REDUCE existing position sizes by 50%
3. TIGHTEN stop losses to 2% maximum
4. MONITOR liquidation distance every 30 seconds
5. PREPARE for emergency position closure

LIQUIDATION DISTANCE: {distance_pct:.1f}%
TIME TO LIQUIDATION: {time_estimate}
EMERGENCY THRESHOLD: {emergency_threshold:.1f}%

DO NOT IGNORE THIS WARNING - ACCOUNT AT RISK
"""
        }
        
        # 📊 POSITION MONITORING: Active position tracking
        self.active_positions = {}
        self.liquidation_alerts = []
        self.risk_history = []
        
        logger.info(f"🚨 Liquidation Awareness System initialized for ${account_balance} account")
        logger.info(f"🛡️ Safety thresholds: Safe >{self.liquidation_thresholds['safe']}%, "
                   f"Warning >{self.liquidation_thresholds['warning']}%, "
                   f"Danger >{self.liquidation_thresholds['danger']}%, "
                   f"Critical <{self.liquidation_thresholds['critical']}%")
    
    def calculate_liquidation_risk(self, symbol: str, side: str, entry_price: float, 
                                 current_price: float, position_size: float, 
                                 leverage: float) -> PositionRisk:
        """
        Calculate comprehensive liquidation risk for a position
        
        Args:
            symbol: Trading symbol
            side: 'LONG' or 'SHORT'
            entry_price: Position entry price
            current_price: Current market price
            position_size: Position size in USDT
            leverage: Leverage used
            
        Returns:
            PositionRisk with detailed risk assessment
        """
        try:
            # Calculate liquidation price
            maintenance_margin_rate = 0.005  # 0.5% maintenance margin
            
            if side.upper() == 'LONG':
                liquidation_price = entry_price * (1 - (1/leverage) + maintenance_margin_rate)
                distance_abs = current_price - liquidation_price
                distance_pct = (distance_abs / current_price) * 100
            else:  # SHORT
                liquidation_price = entry_price * (1 + (1/leverage) - maintenance_margin_rate)
                distance_abs = liquidation_price - current_price
                distance_pct = (distance_abs / current_price) * 100
            
            # Determine risk level
            if distance_pct >= self.liquidation_thresholds['safe']:
                risk_level = 'SAFE'
            elif distance_pct >= self.liquidation_thresholds['warning']:
                risk_level = 'WARNING'
            elif distance_pct >= self.liquidation_thresholds['danger']:
                risk_level = 'DANGER'
            else:
                risk_level = 'CRITICAL'
            
            # Calculate margin ratio
            required_margin = position_size / leverage
            margin_ratio = (required_margin / self.account_balance) * 100
            
            # Calculate unrealized PnL
            if side.upper() == 'LONG':
                unrealized_pnl = (current_price - entry_price) * (position_size / entry_price)
            else:
                unrealized_pnl = (entry_price - current_price) * (position_size / entry_price)
            
            return PositionRisk(
                symbol=symbol,
                side=side,
                entry_price=entry_price,
                current_price=current_price,
                position_size=position_size,
                leverage=leverage,
                liquidation_price=liquidation_price,
                distance_to_liquidation_pct=distance_pct,
                risk_level=risk_level,
                margin_ratio=margin_ratio,
                unrealized_pnl=unrealized_pnl
            )
            
        except Exception as e:
            logger.error(f"Error calculating liquidation risk: {e}")
            return self._create_safe_position_risk(symbol, side, entry_price, current_price, position_size, leverage)
    
    def generate_llm_liquidation_prompt(self, position_risk: PositionRisk) -> str:
        """
        Generate liquidation-aware prompt for LLM decision making
        
        Args:
            position_risk: Current position risk assessment
            
        Returns:
            Formatted prompt with liquidation awareness
        """
        try:
            # Determine current status and recommended action
            if position_risk.risk_level == 'SAFE':
                current_status = "🟢 SAFE - Normal trading operations"
                recommended_action = "Continue monitoring, normal position management"
            elif position_risk.risk_level == 'WARNING':
                current_status = "🟡 WARNING - Increased monitoring required"
                recommended_action = "Reduce position size by 25%, tighten stop losses"
            elif position_risk.risk_level == 'DANGER':
                current_status = "🟠 DANGER - High liquidation risk"
                recommended_action = "Reduce position size by 50%, no new positions"
            else:  # CRITICAL
                current_status = "🔴 CRITICAL - Immediate action required"
                recommended_action = "CLOSE POSITION IMMEDIATELY or add margin"
            
            # Format the prompt
            prompt = self.llm_awareness_prompts['position_analysis'].format(
                distance_pct=position_risk.distance_to_liquidation_pct,
                liquidation_price=position_risk.liquidation_price,
                current_price=position_risk.current_price,
                risk_level=position_risk.risk_level,
                margin_ratio=position_risk.margin_ratio,
                current_status=current_status,
                recommended_action=recommended_action
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating LLM liquidation prompt: {e}")
            return "🚨 LIQUIDATION MONITORING ERROR - USE EXTREME CAUTION"
    
    def check_liquidation_alerts(self, positions: List[PositionRisk]) -> List[LiquidationAlert]:
        """
        Check all positions for liquidation alerts
        
        Args:
            positions: List of current positions
            
        Returns:
            List of liquidation alerts
        """
        alerts = []
        
        for position in positions:
            try:
                if position.distance_to_liquidation_pct <= self.liquidation_thresholds['critical']:
                    alert = LiquidationAlert(
                        alert_type='CRITICAL',
                        distance_pct=position.distance_to_liquidation_pct,
                        liquidation_price=position.liquidation_price,
                        current_price=position.current_price,
                        position_side=position.side,
                        recommended_action='CLOSE POSITION IMMEDIATELY',
                        urgency_level=10,
                        message=f"🔴 CRITICAL: {position.symbol} {position.side} position at {position.distance_to_liquidation_pct:.1f}% from liquidation!"
                    )
                    alerts.append(alert)
                    
                elif position.distance_to_liquidation_pct <= self.liquidation_thresholds['danger']:
                    alert = LiquidationAlert(
                        alert_type='DANGER',
                        distance_pct=position.distance_to_liquidation_pct,
                        liquidation_price=position.liquidation_price,
                        current_price=position.current_price,
                        position_side=position.side,
                        recommended_action='Reduce position size by 50%',
                        urgency_level=8,
                        message=f"🟠 DANGER: {position.symbol} {position.side} position at {position.distance_to_liquidation_pct:.1f}% from liquidation"
                    )
                    alerts.append(alert)
                    
                elif position.distance_to_liquidation_pct <= self.liquidation_thresholds['warning']:
                    alert = LiquidationAlert(
                        alert_type='WARNING',
                        distance_pct=position.distance_to_liquidation_pct,
                        liquidation_price=position.liquidation_price,
                        current_price=position.current_price,
                        position_side=position.side,
                        recommended_action='Monitor closely, consider reducing position',
                        urgency_level=5,
                        message=f"🟡 WARNING: {position.symbol} {position.side} position at {position.distance_to_liquidation_pct:.1f}% from liquidation"
                    )
                    alerts.append(alert)
                    
            except Exception as e:
                logger.error(f"Error checking liquidation alert for position: {e}")
                continue
        
        # Store alerts for history
        self.liquidation_alerts.extend(alerts)
        
        return alerts
    
    def get_safe_position_size(self, symbol: str, leverage: float, current_price: float, 
                             confidence: float = 0.8) -> float:
        """
        Calculate maximum safe position size to prevent liquidation
        
        Args:
            symbol: Trading symbol
            leverage: Intended leverage
            current_price: Current market price
            confidence: Signal confidence (affects position size)
            
        Returns:
            Maximum safe position size in USDT
        """
        try:
            # Calculate maximum margin we can safely use (85% of balance)
            max_safe_margin = self.account_balance * 0.85
            
            # Calculate position size that maintains 20% buffer from liquidation
            # For LONG: liquidation occurs at entry_price * (1 - 1/leverage + maintenance_margin)
            # We want current price to be 20% above liquidation price
            buffer_factor = 1.20  # 20% buffer
            maintenance_margin = 0.005  # 0.5%
            
            # Calculate safe position size
            max_position_notional = max_safe_margin * leverage
            
            # Apply confidence scaling
            confidence_factor = 0.5 + (confidence * 0.5)  # Scale from 0.5 to 1.0
            safe_position_size = max_position_notional * confidence_factor
            
            # Ensure we don't exceed account balance even with leverage
            safe_position_size = min(safe_position_size, self.account_balance * leverage * 0.8)
            
            logger.info(f"💡 Safe position size for {symbol}: ${safe_position_size:.2f} "
                       f"(leverage: {leverage}x, confidence: {confidence:.1%})")
            
            return safe_position_size
            
        except Exception as e:
            logger.error(f"Error calculating safe position size: {e}")
            return self.account_balance * 0.1  # Very conservative fallback
    
    def _create_safe_position_risk(self, symbol: str, side: str, entry_price: float,
                                 current_price: float, position_size: float, leverage: float) -> PositionRisk:
        """Create a safe fallback position risk assessment"""
        return PositionRisk(
            symbol=symbol,
            side=side,
            entry_price=entry_price,
            current_price=current_price,
            position_size=position_size,
            leverage=leverage,
            liquidation_price=entry_price * 0.5 if side.upper() == 'LONG' else entry_price * 1.5,
            distance_to_liquidation_pct=50.0,  # Assume safe distance
            risk_level='SAFE',
            margin_ratio=10.0,
            unrealized_pnl=0.0
        )
