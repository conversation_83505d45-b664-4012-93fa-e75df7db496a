#!/usr/bin/env python3
"""
Emergency Configuration Validation Script
Validates the PEPE/USDT:USDT emergency symbol switch and workflow
"""

import sys
import os
import json
import time
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def validate_emergency_configuration():
    """Validate the emergency configuration implementation"""
    print("🔍 EMERGENCY CONFIGURATION VALIDATION")
    print("=" * 60)
    
    validation_results = {
        'symbol_scanner_config': False,
        'default_symbol_config': False,
        'gui_defaults': False,
        'emergency_config_file': False,
        'position_sizing': False,
        'workflow_viability': False
    }
    
    # Test 1: Symbol Scanner Configuration
    print("\n1️⃣ Validating Symbol Scanner Configuration...")
    try:
        from core.symbol_scanner import SymbolScannerConfig
        default_symbols = SymbolScannerConfig.DEFAULT_SYMBOLS
        
        if default_symbols[0] == 'PEPE/USDT:USDT':
            print("  ✅ PEPE/USDT:USDT is first priority symbol")
            validation_results['symbol_scanner_config'] = True
        else:
            print(f"  ❌ First symbol is {default_symbols[0]}, expected PEPE/USDT:USDT")
            
        print(f"  📊 Symbol priority order: {default_symbols[:3]}")
        
    except Exception as e:
        print(f"  ❌ Error validating symbol scanner: {e}")
    
    # Test 2: Default Symbol Configuration
    print("\n2️⃣ Validating Default Symbol Configuration...")
    try:
        from config.config import DEFAULT_SYMBOL
        
        if 'PEPE' in DEFAULT_SYMBOL:
            print(f"  ✅ Default symbol updated: {DEFAULT_SYMBOL}")
            validation_results['default_symbol_config'] = True
        else:
            print(f"  ❌ Default symbol not updated: {DEFAULT_SYMBOL}")
            
    except Exception as e:
        print(f"  ❌ Error validating default symbol: {e}")
    
    # Test 3: Emergency Config File
    print("\n3️⃣ Validating Emergency Config File...")
    try:
        if os.path.exists('emergency_trading_config.json'):
            with open('emergency_trading_config.json', 'r') as f:
                emergency_config = json.load(f)
            
            if emergency_config.get('emergency_mode') and emergency_config.get('emergency_configuration', {}).get('symbol') == 'PEPE/USDT:USDT':
                print("  ✅ Emergency configuration file valid")
                print(f"  📊 Emergency symbol: {emergency_config['emergency_configuration']['symbol']}")
                print(f"  💰 Minimum order: ${emergency_config['emergency_configuration']['minimum_order_value']}")
                validation_results['emergency_config_file'] = True
            else:
                print("  ❌ Emergency configuration file invalid")
        else:
            print("  ❌ Emergency configuration file not found")
            
    except Exception as e:
        print(f"  ❌ Error validating emergency config: {e}")
    
    # Test 4: Position Sizing Validation
    print("\n4️⃣ Validating Position Sizing for PEPE...")
    try:
        account_balance = 19.51
        pepe_min_order = 8.0
        position_size_pct = 80.0
        
        max_position_value = account_balance * (position_size_pct / 100)
        safety_margin = account_balance - max_position_value
        
        print(f"  💰 Account Balance: ${account_balance:.2f}")
        print(f"  📋 PEPE Minimum Order: ${pepe_min_order:.2f}")
        print(f"  🎯 Max Position (80%): ${max_position_value:.2f}")
        print(f"  🛡️ Safety Margin: ${safety_margin:.2f}")
        
        if max_position_value >= pepe_min_order and safety_margin > 0:
            print("  ✅ Position sizing viable for PEPE trading")
            validation_results['position_sizing'] = True
        else:
            print("  ❌ Position sizing not viable")
            
    except Exception as e:
        print(f"  ❌ Error validating position sizing: {e}")
    
    # Test 5: Workflow Viability
    print("\n5️⃣ Validating Modified Workflow...")
    try:
        workflow_steps = [
            "BTC/USDT:USDT (symbol selection)",
            "PEPE/USDT:USDT (emergency switch)",
            "ScalperGPT Auto (autonomous trading)"
        ]
        
        print("  📋 Modified Workflow Steps:")
        for i, step in enumerate(workflow_steps, 1):
            print(f"    {i}. {step}")
        
        # Check if all components are available
        components_available = True
        try:
            from core.scalper_gpt import ScalperGPT
            from core.llm_orchestrator import LLMPromptOrchestrator
            from trading.intelligent_limit_order_manager import IntelligentLimitOrderManager
            print("  ✅ All required components available")
        except ImportError as e:
            print(f"  ❌ Missing component: {e}")
            components_available = False
        
        if components_available:
            validation_results['workflow_viability'] = True
            
    except Exception as e:
        print(f"  ❌ Error validating workflow: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("🎯 VALIDATION SUMMARY")
    print("="*60)
    
    passed_tests = sum(validation_results.values())
    total_tests = len(validation_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"📊 Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    for test_name, result in validation_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name.replace('_', ' ').title()}")
    
    if success_rate >= 80:
        print("\n🎉 EMERGENCY CONFIGURATION VALIDATION SUCCESSFUL")
        print("✅ System ready for PEPE/USDT:USDT autonomous trading")
        return True
    else:
        print("\n⚠️ EMERGENCY CONFIGURATION VALIDATION FAILED")
        print("🔧 Additional fixes required before deployment")
        return False

def test_pepe_trading_capability():
    """Test PEPE trading capability with current balance"""
    print("\n🧪 TESTING PEPE TRADING CAPABILITY")
    print("=" * 60)
    
    try:
        # Simulate PEPE trading parameters (CORRECTED)
        account_balance = 19.51
        pepe_price = 0.000008  # Approximate PEPE price
        min_contracts = 1000000  # PEPE minimum contracts (1M, not 100M)
        min_order_value = min_contracts * pepe_price
        
        print(f"💰 Account Balance: ${account_balance:.2f}")
        print(f"📏 PEPE Price: ${pepe_price:.8f}")
        print(f"📋 Minimum Contracts: {min_contracts:,}")
        print(f"📊 Minimum Order Value: ${min_order_value:.2f}")
        
        # Test position sizing
        position_sizes = [0.25, 0.50, 0.80]  # 25%, 50%, 80%
        
        print(f"\n📊 POSITION SIZING TESTS:")
        for pct in position_sizes:
            position_value = account_balance * pct
            contracts = int(position_value / pepe_price)
            
            viable = contracts >= min_contracts and position_value >= min_order_value
            status = "✅ VIABLE" if viable else "❌ NOT VIABLE"
            
            print(f"  {pct*100:2.0f}% Position: ${position_value:5.2f} = {contracts:,} contracts {status}")
        
        # Test emergency configuration
        emergency_position = account_balance * 0.80  # 80% as configured
        emergency_contracts = int(emergency_position / pepe_price)
        emergency_viable = emergency_contracts >= min_contracts
        
        print(f"\n🚨 EMERGENCY CONFIGURATION TEST:")
        print(f"  Position Value: ${emergency_position:.2f}")
        print(f"  Contracts: {emergency_contracts:,}")
        print(f"  Viable: {'✅ YES' if emergency_viable else '❌ NO'}")
        print(f"  Safety Margin: ${account_balance - emergency_position:.2f}")
        
        return emergency_viable
        
    except Exception as e:
        print(f"❌ Error testing PEPE capability: {e}")
        return False

if __name__ == "__main__":
    print("🚀 STARTING EMERGENCY CONFIGURATION VALIDATION")
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run validation
    config_valid = validate_emergency_configuration()
    pepe_viable = test_pepe_trading_capability()
    
    print(f"\n{'='*60}")
    print("🏁 FINAL VALIDATION RESULT")
    print("="*60)
    
    if config_valid and pepe_viable:
        print("🎉 EMERGENCY CONFIGURATION FULLY VALIDATED")
        print("✅ Ready to proceed with PEPE autonomous trading")
        exit_code = 0
    else:
        print("❌ EMERGENCY CONFIGURATION VALIDATION FAILED")
        print("🔧 Manual intervention required")
        exit_code = 1
    
    print(f"\n⏰ Validation completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    sys.exit(exit_code)
