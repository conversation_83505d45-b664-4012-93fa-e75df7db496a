#!/usr/bin/env python3
"""
🚀 PRODUCTION READINESS VALIDATION
Final validation for DOGE scalping system production deployment
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_production_readiness():
    """Final production readiness validation"""
    print("🚀 PRODUCTION READINESS VALIDATION")
    print("=" * 60)
    
    production_metrics = {
        'data_pipeline': 0,
        'decision_making': 0,
        'risk_management': 0,
        'execution_readiness': 0,
        'system_stability': 0
    }
    
    try:
        # Test 1: Data Pipeline Stability
        print("\n1️⃣ Testing Data Pipeline Stability...")
        
        from data.live_data_manager import LiveDataManager
        
        live_data_manager = LiveDataManager()
        test_symbol = "DOGE/USDT:USDT"
        
        # Test data consistency over time
        live_data_manager.subscribe_symbol(test_symbol, ['1m', '5m'])
        time.sleep(3)
        
        # Multiple data fetches to test consistency
        data_consistency_tests = []
        for i in range(3):
            candles = live_data_manager.get_chart_data(test_symbol, '1m', limit=30)
            trades = live_data_manager.get_recent_trades(test_symbol, limit=50)
            orderbook = live_data_manager.get_latest_orderbook(test_symbol)
            
            test_result = {
                'candles': len(candles) if candles else 0,
                'trades': len(trades) if trades else 0,
                'orderbook_valid': bool(orderbook and orderbook.get('bids') and orderbook.get('asks'))
            }
            data_consistency_tests.append(test_result)
            time.sleep(1)
        
        # Calculate data pipeline score
        avg_candles = sum(t['candles'] for t in data_consistency_tests) / len(data_consistency_tests)
        avg_trades = sum(t['trades'] for t in data_consistency_tests) / len(data_consistency_tests)
        orderbook_reliability = sum(t['orderbook_valid'] for t in data_consistency_tests) / len(data_consistency_tests)
        
        pipeline_score = 0
        if avg_candles >= 25: pipeline_score += 30
        if avg_trades >= 40: pipeline_score += 30
        if orderbook_reliability >= 0.8: pipeline_score += 40
        
        production_metrics['data_pipeline'] = pipeline_score
        
        print(f"  📊 Average Candles: {avg_candles:.1f}/30")
        print(f"  📊 Average Trades: {avg_trades:.1f}/50")
        print(f"  📊 Orderbook Reliability: {orderbook_reliability*100:.1f}%")
        print(f"  🎯 Data Pipeline Score: {pipeline_score}/100")
        
        # Test 2: Decision Making Consistency
        print("\n2️⃣ Testing Decision Making Consistency...")
        
        from core.llm_orchestrator import LLMPromptOrchestrator, LmStudioRunner
        
        lmstudio_runner = LmStudioRunner()
        orchestrator = LLMPromptOrchestrator(lmstudio_runner)
        
        # Test decision consistency with same inputs
        test_scenarios = [
            {
                'name': 'STRONG_BULLISH',
                'results': {
                    'risk_assessment': type('MockResult', (), {
                        'response': {'APPROVED': True, 'CONFIDENCE': 95},
                        'success': True
                    })(),
                    'entry_timing': type('MockResult', (), {
                        'response': {'ACTION': 'ENTER_NOW', 'DIRECTION': 'LONG', 'CONFIDENCE': 90},
                        'success': True
                    })(),
                    'opportunity_scanner': type('MockResult', (), {
                        'response': {'BEST_OPPORTUNITY': 'BULLISH_BREAKOUT', 'DECISION': 'LONG', 'CONFIDENCE': 85},
                        'success': True
                    })()
                },
                'expected': 'LONG'
            },
            {
                'name': 'STRONG_BEARISH',
                'results': {
                    'risk_assessment': type('MockResult', (), {
                        'response': {'APPROVED': True, 'CONFIDENCE': 90},
                        'success': True
                    })(),
                    'entry_timing': type('MockResult', (), {
                        'response': {'ACTION': 'ENTER_NOW', 'DIRECTION': 'SHORT', 'CONFIDENCE': 95},
                        'success': True
                    })(),
                    'opportunity_scanner': type('MockResult', (), {
                        'response': {'BEST_OPPORTUNITY': 'BEARISH_BREAKDOWN', 'DECISION': 'SHORT', 'CONFIDENCE': 88},
                        'success': True
                    })()
                },
                'expected': 'SHORT'
            }
        ]
        
        decision_consistency = []
        for scenario in test_scenarios:
            decisions = []
            for _ in range(3):  # Test same scenario 3 times
                decision = orchestrator.get_aggregated_decision(scenario['results'])
                decisions.append(decision.decision.value)
            
            # Check consistency
            consistent = all(d == scenario['expected'] for d in decisions)
            decision_consistency.append(consistent)
            
            print(f"  🎯 {scenario['name']}: {decisions} - {'✅ CONSISTENT' if consistent else '❌ INCONSISTENT'}")
        
        decision_score = (sum(decision_consistency) / len(decision_consistency)) * 100
        production_metrics['decision_making'] = decision_score
        
        print(f"  📊 Decision Consistency: {decision_score:.1f}%")
        
        # Test 3: Risk Management Validation
        print("\n3️⃣ Testing Risk Management...")
        
        # Test configuration values
        risk_configs = {
            'liquidity_threshold': 0.1,  # Should be low for crypto
            'max_spread': 3.0,  # Should be reasonable for crypto
            'position_limits': True,  # Should have position limits
            'stop_loss': True  # Should have stop loss mechanisms
        }
        
        # Read actual config from intelligent limit order manager
        from trading.intelligent_limit_order_manager import IntelligentLimitOrderManager
        
        # Create minimal mock for testing
        class MinimalMockEngine:
            def place_limit_order(self, *args, **kwargs):
                return {'status': 'success', 'order_id': 'test'}
        
        class MinimalMockData:
            def get_latest_orderbook(self, symbol):
                return {'bids': [[0.4, 1.0]], 'asks': [[0.41, 1.0]]}
        
        try:
            order_manager = IntelligentLimitOrderManager(MinimalMockEngine(), MinimalMockData())
            actual_threshold = order_manager.config.get('min_order_book_depth', 50)
            actual_spread = order_manager.config.get('max_spread_pct', 0.1)
            
            risk_score = 0
            if actual_threshold <= 1.0: risk_score += 25
            if actual_spread >= 1.0: risk_score += 25
            if hasattr(order_manager, 'config'): risk_score += 25
            if 'default_timeout_seconds' in order_manager.config: risk_score += 25
            
            production_metrics['risk_management'] = risk_score
            
            print(f"  📊 Liquidity Threshold: {actual_threshold} {'✅' if actual_threshold <= 1.0 else '❌'}")
            print(f"  📊 Max Spread: {actual_spread}% {'✅' if actual_spread >= 1.0 else '❌'}")
            print(f"  🎯 Risk Management Score: {risk_score}/100")
            
        except Exception as e:
            print(f"  ❌ Risk management test error: {e}")
            production_metrics['risk_management'] = 0
        
        # Test 4: Execution Readiness
        print("\n4️⃣ Testing Execution Readiness...")
        
        # Test system components integration
        execution_components = {
            'live_data_manager': live_data_manager is not None,
            'llm_orchestrator': orchestrator is not None,
            'order_manager': order_manager is not None,
            'data_flowing': avg_candles > 0 and avg_trades > 0
        }
        
        execution_score = (sum(execution_components.values()) / len(execution_components)) * 100
        production_metrics['execution_readiness'] = execution_score
        
        for component, status in execution_components.items():
            print(f"  🎯 {component.replace('_', ' ').title()}: {'✅ READY' if status else '❌ NOT READY'}")
        
        print(f"  📊 Execution Readiness: {execution_score:.1f}%")
        
        # Test 5: System Stability
        print("\n5️⃣ Testing System Stability...")
        
        # Test error handling and recovery
        stability_tests = []
        
        # Test 1: Invalid symbol handling
        try:
            invalid_data = live_data_manager.get_chart_data("INVALID/SYMBOL", '1m', limit=10)
            stability_tests.append(True)  # Should handle gracefully
        except Exception:
            stability_tests.append(False)
        
        # Test 2: Empty data handling
        try:
            empty_decision = orchestrator.get_aggregated_decision({})
            stability_tests.append(empty_decision is not None)
        except Exception:
            stability_tests.append(False)
        
        # Test 3: Configuration consistency
        try:
            config_test = order_manager.config.get('min_order_book_depth') is not None
            stability_tests.append(config_test)
        except Exception:
            stability_tests.append(False)
        
        stability_score = (sum(stability_tests) / len(stability_tests)) * 100
        production_metrics['system_stability'] = stability_score
        
        print(f"  🎯 Error Handling: {'✅ ROBUST' if stability_tests[0] else '❌ FRAGILE'}")
        print(f"  🎯 Empty Data Handling: {'✅ ROBUST' if stability_tests[1] else '❌ FRAGILE'}")
        print(f"  🎯 Configuration Integrity: {'✅ VALID' if stability_tests[2] else '❌ INVALID'}")
        print(f"  📊 System Stability: {stability_score:.1f}%")
        
        # Final Production Assessment
        print(f"\n{'='*60}")
        print("🚀 PRODUCTION READINESS ASSESSMENT")
        print("="*60)
        
        total_score = sum(production_metrics.values()) / len(production_metrics)
        
        print(f"\n📊 PRODUCTION METRICS:")
        for metric, score in production_metrics.items():
            status = "✅ EXCELLENT" if score >= 80 else "⚠️ GOOD" if score >= 60 else "❌ NEEDS WORK"
            print(f"  {metric.replace('_', ' ').title()}: {score:.1f}% {status}")
        
        print(f"\n🎯 Overall Production Score: {total_score:.1f}%")
        
        # Production Deployment Decision
        if total_score >= 80:
            print("\n🚀 SYSTEM READY FOR PRODUCTION DEPLOYMENT")
            print("✅ All critical systems meet production standards")
            
            print("\n📋 PRODUCTION DEPLOYMENT PLAN:")
            print("1. 🎯 Start with ULTRA-CONSERVATIVE settings:")
            print("   - Position size: 1% of balance ($0.18 with $18.46 balance)")
            print("   - Max daily trades: 5")
            print("   - Stop loss: 2% per trade")
            print("   - Max daily loss: 5% of balance")
            
            print("\n2. 📊 MONITORING REQUIREMENTS:")
            print("   - Monitor first 10 trades manually")
            print("   - Verify both LONG and SHORT executions")
            print("   - Track win rate and profit factor")
            print("   - Log all order executions and fills")
            
            print("\n3. 🔄 SCALING STRATEGY:")
            print("   - Week 1: 1% position size, max 5 trades/day")
            print("   - Week 2: 2% position size if >60% win rate")
            print("   - Week 3: 3% position size if profitable")
            print("   - Scale gradually based on performance")
            
        elif total_score >= 60:
            print("\n⚠️ SYSTEM NEEDS MINOR IMPROVEMENTS")
            print("🔧 Address low-scoring areas before full deployment")
            
        else:
            print("\n❌ SYSTEM NOT READY FOR PRODUCTION")
            print("🚨 Critical improvements needed")
        
        print(f"\nProduction assessment completed at: {datetime.now()}")
        return total_score >= 80
        
    except Exception as e:
        print(f"❌ Critical error in production readiness test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_production_readiness()
    sys.exit(0 if success else 1)
