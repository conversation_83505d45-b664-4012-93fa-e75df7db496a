"""
Outcome-Based Prompt Optimization System
Analyzes trade outcomes to optimize LLM prompts for better performance
"""

import json
import logging
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class TradeOutcome:
    """Complete trade outcome record"""
    trade_id: str
    timestamp: datetime
    symbol: str
    
    # Pre-trade data
    prompt_text: str
    prompt_hash: str
    input_variables: Dict[str, Any]
    market_context: Dict[str, Any]
    
    # LLM response
    llm_response: Dict[str, Any]
    confidence: float
    reasoning: str
    decision: str
    
    # Trade execution
    entry_price: float
    exit_price: Optional[float]
    position_size: float
    hold_time_seconds: int
    
    # Trade results
    pnl_gross: float
    pnl_net: float  # After fees
    fees_paid: float
    slippage: float
    win: bool
    
    # Performance metrics
    return_pct: float
    risk_adjusted_return: float
    execution_quality: float

@dataclass
class PromptPerformance:
    """Performance metrics for a prompt template"""
    prompt_hash: str
    prompt_template: str
    
    # Usage statistics
    total_trades: int
    total_samples: int
    
    # Performance metrics
    win_rate: float
    avg_return: float
    avg_risk_adjusted_return: float
    sharpe_ratio: float
    max_drawdown: float
    
    # Confidence calibration
    confidence_accuracy: float  # How well confidence predicts outcomes
    avg_confidence: float
    
    # Market context performance
    regime_performance: Dict[str, float]  # Performance by market regime
    volatility_performance: Dict[str, float]  # Performance by volatility level
    
    # Last updated
    last_updated: datetime

class PromptOptimizer:
    """Outcome-based prompt optimization system"""
    
    def __init__(self, db_path: str = "data/prompt_optimization.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Prompt templates for A/B testing
        self.active_templates = {}
        self.template_weights = {}  # For weighted random selection
        
        # Performance tracking
        self.optimization_history = []
        self.last_optimization = None
        
        # Configuration
        self.min_samples_for_optimization = 50
        self.confidence_threshold = 0.05  # Statistical significance
        self.optimization_frequency_hours = 24
        
    def _init_database(self):
        """Initialize SQLite database for trade outcomes"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create trade outcomes table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_outcomes (
                    trade_id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    symbol TEXT,
                    prompt_text TEXT,
                    prompt_hash TEXT,
                    input_variables TEXT,
                    market_context TEXT,
                    llm_response TEXT,
                    confidence REAL,
                    reasoning TEXT,
                    decision TEXT,
                    entry_price REAL,
                    exit_price REAL,
                    position_size REAL,
                    hold_time_seconds INTEGER,
                    pnl_gross REAL,
                    pnl_net REAL,
                    fees_paid REAL,
                    slippage REAL,
                    win INTEGER,
                    return_pct REAL,
                    risk_adjusted_return REAL,
                    execution_quality REAL
                )
            ''')
            
            # Create prompt performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prompt_performance (
                    prompt_hash TEXT PRIMARY KEY,
                    prompt_template TEXT,
                    total_trades INTEGER,
                    total_samples INTEGER,
                    win_rate REAL,
                    avg_return REAL,
                    avg_risk_adjusted_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    confidence_accuracy REAL,
                    avg_confidence REAL,
                    regime_performance TEXT,
                    volatility_performance TEXT,
                    last_updated TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Prompt optimization database initialized")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def log_trade_outcome(self, outcome: TradeOutcome):
        """Log a complete trade outcome for analysis"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Insert trade outcome
            cursor.execute('''
                INSERT OR REPLACE INTO trade_outcomes VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ''', (
                outcome.trade_id,
                outcome.timestamp.isoformat(),
                outcome.symbol,
                outcome.prompt_text,
                outcome.prompt_hash,
                json.dumps(outcome.input_variables),
                json.dumps(outcome.market_context),
                json.dumps(outcome.llm_response),
                outcome.confidence,
                outcome.reasoning,
                outcome.decision,
                outcome.entry_price,
                outcome.exit_price,
                outcome.position_size,
                outcome.hold_time_seconds,
                outcome.pnl_gross,
                outcome.pnl_net,
                outcome.fees_paid,
                outcome.slippage,
                int(outcome.win),
                outcome.return_pct,
                outcome.risk_adjusted_return,
                outcome.execution_quality
            ))
            
            conn.commit()
            conn.close()
            
            # Update prompt performance
            self._update_prompt_performance(outcome.prompt_hash)
            
            logger.debug(f"Logged trade outcome: {outcome.trade_id}")
            
        except Exception as e:
            logger.error(f"Error logging trade outcome: {e}")
    
    def _update_prompt_performance(self, prompt_hash: str):
        """Update performance metrics for a specific prompt"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all outcomes for this prompt
            cursor.execute('''
                SELECT * FROM trade_outcomes WHERE prompt_hash = ?
            ''', (prompt_hash,))
            
            rows = cursor.fetchall()
            if not rows:
                conn.close()
                return
            
            # Calculate performance metrics
            outcomes = []
            for row in rows:
                outcomes.append({
                    'win': bool(row[19]),
                    'return_pct': row[20],
                    'risk_adjusted_return': row[21],
                    'confidence': row[8],
                    'market_context': json.loads(row[6]) if row[6] else {}
                })
            
            performance = self._calculate_prompt_performance(outcomes)
            
            # Get prompt template
            cursor.execute('''
                SELECT prompt_text FROM trade_outcomes WHERE prompt_hash = ? LIMIT 1
            ''', (prompt_hash,))
            prompt_template = cursor.fetchone()[0]
            
            # Update performance table
            cursor.execute('''
                INSERT OR REPLACE INTO prompt_performance VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ''', (
                prompt_hash,
                prompt_template,
                performance['total_trades'],
                performance['total_samples'],
                performance['win_rate'],
                performance['avg_return'],
                performance['avg_risk_adjusted_return'],
                performance['sharpe_ratio'],
                performance['max_drawdown'],
                performance['confidence_accuracy'],
                performance['avg_confidence'],
                json.dumps(performance['regime_performance']),
                json.dumps(performance['volatility_performance']),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error updating prompt performance: {e}")
    
    def _calculate_prompt_performance(self, outcomes: List[Dict]) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        try:
            if not outcomes:
                return self._get_default_performance()
            
            # Basic metrics
            total_trades = len(outcomes)
            wins = sum(1 for o in outcomes if o['win'])
            win_rate = wins / total_trades if total_trades > 0 else 0
            
            returns = [o['return_pct'] for o in outcomes]
            risk_adj_returns = [o['risk_adjusted_return'] for o in outcomes]
            confidences = [o['confidence'] for o in outcomes]
            
            avg_return = np.mean(returns) if returns else 0
            avg_risk_adjusted_return = np.mean(risk_adj_returns) if risk_adj_returns else 0
            avg_confidence = np.mean(confidences) if confidences else 0
            
            # Sharpe ratio
            sharpe_ratio = np.mean(returns) / np.std(returns) if len(returns) > 1 and np.std(returns) > 0 else 0
            
            # Maximum drawdown
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = running_max - cumulative_returns
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
            
            # Confidence calibration
            confidence_accuracy = self._calculate_confidence_accuracy(outcomes)
            
            # Performance by market context
            regime_performance = self._calculate_regime_performance(outcomes)
            volatility_performance = self._calculate_volatility_performance(outcomes)
            
            return {
                'total_trades': total_trades,
                'total_samples': total_trades,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'avg_risk_adjusted_return': avg_risk_adjusted_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'confidence_accuracy': confidence_accuracy,
                'avg_confidence': avg_confidence,
                'regime_performance': regime_performance,
                'volatility_performance': volatility_performance
            }
            
        except Exception as e:
            logger.error(f"Error calculating prompt performance: {e}")
            return self._get_default_performance()
    
    def _calculate_confidence_accuracy(self, outcomes: List[Dict]) -> float:
        """Calculate how well confidence predicts actual outcomes"""
        try:
            if len(outcomes) < 10:
                return 0.5
            
            # Bin outcomes by confidence level
            confidence_bins = np.linspace(0.5, 1.0, 6)  # 50%, 60%, 70%, 80%, 90%, 100%
            bin_accuracies = []
            
            for i in range(len(confidence_bins) - 1):
                bin_min = confidence_bins[i]
                bin_max = confidence_bins[i + 1]
                
                bin_outcomes = [o for o in outcomes if bin_min <= o['confidence'] < bin_max]
                if len(bin_outcomes) >= 5:  # Minimum samples per bin
                    bin_accuracy = sum(1 for o in bin_outcomes if o['win']) / len(bin_outcomes)
                    expected_accuracy = (bin_min + bin_max) / 2
                    
                    # Calculate accuracy relative to expected
                    relative_accuracy = 1.0 - abs(bin_accuracy - expected_accuracy)
                    bin_accuracies.append(relative_accuracy)
            
            return np.mean(bin_accuracies) if bin_accuracies else 0.5
            
        except Exception as e:
            logger.error(f"Error calculating confidence accuracy: {e}")
            return 0.5
    
    def _calculate_regime_performance(self, outcomes: List[Dict]) -> Dict[str, float]:
        """Calculate performance by market regime"""
        try:
            regime_performance = {}
            
            for outcome in outcomes:
                market_context = outcome.get('market_context', {})
                regime = market_context.get('regime', 'UNKNOWN')
                
                if regime not in regime_performance:
                    regime_performance[regime] = []
                
                regime_performance[regime].append(outcome['return_pct'])
            
            # Calculate average return for each regime
            for regime in regime_performance:
                returns = regime_performance[regime]
                regime_performance[regime] = np.mean(returns) if returns else 0
            
            return regime_performance
            
        except Exception as e:
            logger.error(f"Error calculating regime performance: {e}")
            return {}
    
    def _calculate_volatility_performance(self, outcomes: List[Dict]) -> Dict[str, float]:
        """Calculate performance by volatility level"""
        try:
            volatility_performance = {'LOW': [], 'MEDIUM': [], 'HIGH': []}
            
            for outcome in outcomes:
                market_context = outcome.get('market_context', {})
                volatility = market_context.get('volatility', 1.0)
                
                # Classify volatility
                if volatility < 0.5:
                    vol_category = 'LOW'
                elif volatility < 1.5:
                    vol_category = 'MEDIUM'
                else:
                    vol_category = 'HIGH'
                
                volatility_performance[vol_category].append(outcome['return_pct'])
            
            # Calculate average return for each volatility level
            for vol_level in volatility_performance:
                returns = volatility_performance[vol_level]
                volatility_performance[vol_level] = np.mean(returns) if returns else 0
            
            return volatility_performance
            
        except Exception as e:
            logger.error(f"Error calculating volatility performance: {e}")
            return {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0}
    
    def _get_default_performance(self) -> Dict[str, Any]:
        """Get default performance metrics"""
        return {
            'total_trades': 0,
            'total_samples': 0,
            'win_rate': 0.5,
            'avg_return': 0.0,
            'avg_risk_adjusted_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'confidence_accuracy': 0.5,
            'avg_confidence': 0.7,
            'regime_performance': {},
            'volatility_performance': {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0}
        }
    
    def get_best_prompt_template(self, market_context: Dict[str, Any]) -> Optional[str]:
        """Get the best performing prompt template for current market conditions"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all prompt performances
            cursor.execute('''
                SELECT prompt_hash, prompt_template, win_rate, avg_risk_adjusted_return, 
                       total_trades, regime_performance, volatility_performance
                FROM prompt_performance
                WHERE total_trades >= ?
            ''', (self.min_samples_for_optimization,))
            
            rows = cursor.fetchall()
            conn.close()
            
            if not rows:
                return None
            
            # Score prompts based on current market context
            best_score = -float('inf')
            best_template = None
            
            current_regime = market_context.get('regime', 'UNKNOWN')
            current_volatility = market_context.get('volatility', 1.0)
            
            for row in rows:
                prompt_hash, template, win_rate, avg_return, total_trades, regime_perf_json, vol_perf_json = row
                
                # Base score from overall performance
                base_score = (win_rate * 0.6) + (avg_return * 0.4)
                
                # Adjust for market context
                regime_performance = json.loads(regime_perf_json) if regime_perf_json else {}
                vol_performance = json.loads(vol_perf_json) if vol_perf_json else {}
                
                # Regime adjustment
                regime_bonus = regime_performance.get(current_regime, 0) * 0.2
                
                # Volatility adjustment
                vol_category = 'LOW' if current_volatility < 0.5 else 'HIGH' if current_volatility > 1.5 else 'MEDIUM'
                vol_bonus = vol_performance.get(vol_category, 0) * 0.2
                
                # Sample size bonus (more samples = more reliable)
                sample_bonus = min(0.1, total_trades / 1000)
                
                total_score = base_score + regime_bonus + vol_bonus + sample_bonus
                
                if total_score > best_score:
                    best_score = total_score
                    best_template = template
            
            return best_template
            
        except Exception as e:
            logger.error(f"Error getting best prompt template: {e}")
            return None
    
    def optimize_prompts(self) -> Dict[str, Any]:
        """Run prompt optimization analysis"""
        try:
            # Check if optimization is needed
            if (self.last_optimization and 
                datetime.now() - self.last_optimization < timedelta(hours=self.optimization_frequency_hours)):
                return {'status': 'skipped', 'reason': 'too_recent'}
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get all prompt performances
            cursor.execute('''
                SELECT prompt_hash, win_rate, avg_risk_adjusted_return, total_trades
                FROM prompt_performance
                WHERE total_trades >= ?
                ORDER BY avg_risk_adjusted_return DESC
            ''', (self.min_samples_for_optimization,))
            
            rows = cursor.fetchall()
            conn.close()
            
            if len(rows) < 2:
                return {'status': 'insufficient_data', 'templates': len(rows)}
            
            # Analyze performance differences
            best_performance = rows[0]
            worst_performance = rows[-1]
            
            performance_improvement = best_performance[2] - worst_performance[2]  # Risk-adjusted return difference
            
            optimization_result = {
                'status': 'completed',
                'timestamp': datetime.now().isoformat(),
                'templates_analyzed': len(rows),
                'best_template_hash': best_performance[0],
                'best_win_rate': best_performance[1],
                'best_return': best_performance[2],
                'performance_improvement': performance_improvement,
                'recommendations': self._generate_optimization_recommendations(rows)
            }
            
            self.optimization_history.append(optimization_result)
            self.last_optimization = datetime.now()
            
            logger.info(f"Prompt optimization completed: {performance_improvement:.4f} improvement potential")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error in prompt optimization: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _generate_optimization_recommendations(self, performance_data: List[Tuple]) -> List[str]:
        """Generate optimization recommendations based on performance analysis"""
        recommendations = []
        
        try:
            if len(performance_data) >= 2:
                best = performance_data[0]
                worst = performance_data[-1]
                
                win_rate_diff = best[1] - worst[1]
                return_diff = best[2] - worst[2]
                
                if win_rate_diff > 0.1:
                    recommendations.append(f"Significant win rate difference detected ({win_rate_diff:.1%}). Consider using best performing template.")
                
                if return_diff > 0.02:
                    recommendations.append(f"Risk-adjusted return difference of {return_diff:.2%} suggests prompt optimization potential.")
                
                if len(performance_data) > 5:
                    recommendations.append("Multiple templates available. Consider A/B testing with top performers.")
                
                # Sample size recommendations
                low_sample_templates = [p for p in performance_data if p[3] < 100]
                if low_sample_templates:
                    recommendations.append(f"{len(low_sample_templates)} templates need more samples for reliable analysis.")
            
            if not recommendations:
                recommendations.append("Performance differences are minimal. Current prompts are well-optimized.")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]
    
    def create_prompt_hash(self, prompt_text: str) -> str:
        """Create a hash for prompt template identification"""
        return hashlib.md5(prompt_text.encode()).hexdigest()[:16]
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization system statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Count total outcomes
            cursor.execute('SELECT COUNT(*) FROM trade_outcomes')
            total_outcomes = cursor.fetchone()[0]
            
            # Count unique prompts
            cursor.execute('SELECT COUNT(DISTINCT prompt_hash) FROM trade_outcomes')
            unique_prompts = cursor.fetchone()[0]
            
            # Get performance summary
            cursor.execute('''
                SELECT AVG(win_rate), AVG(avg_risk_adjusted_return), COUNT(*)
                FROM prompt_performance
                WHERE total_trades >= ?
            ''', (self.min_samples_for_optimization,))
            
            perf_summary = cursor.fetchone()
            conn.close()
            
            return {
                'total_trade_outcomes': total_outcomes,
                'unique_prompt_templates': unique_prompts,
                'optimizable_templates': perf_summary[2] if perf_summary else 0,
                'average_win_rate': perf_summary[0] if perf_summary and perf_summary[0] else 0,
                'average_risk_adjusted_return': perf_summary[1] if perf_summary and perf_summary[1] else 0,
                'last_optimization': self.last_optimization.isoformat() if self.last_optimization else None,
                'optimization_runs': len(self.optimization_history)
            }
            
        except Exception as e:
            logger.error(f"Error getting optimization stats: {e}")
            return {}
