ml:
  adaptation_window: 100
  ensemble_weights:
    lstm: 0.20              # Reduced - less reliable for crypto scalping
    orderflow: 0.15         # Increased - critical for crypto futures
    random_forest: 0.22     # Slightly reduced
    rsi: 0.08              # Increased - important for crypto momentum
    sentiment: 0.03        # Reduced - less reliable for scalping
    svm: 0.22              # Slightly reduced
    volatility: 0.10       # Doubled - volatility key in crypto
    vwap: 0.00             # Removed - less relevant for scalping
  learning_rate: 0.015     # Increased for faster adaptation
  model_weights:
    lstm: 0.9              # Reduced weight
    random_forest: 1.1     # Increased weight
    svm: 1.0               # Unchanged
  performance_threshold: 0.58  # Increased for better quality
  update_frequency: 18     # More frequent updates for crypto
monitoring:
  alert_thresholds:
    daily_loss: -0.05
    drawdown: -0.1
    win_rate: 0.4
  db_path: data/trading_performance.db
  enable_metrics: true
  log_level: INFO
  metrics_interval: 60
risk:
  max_concurrent_positions: 2    # Reduced for better focus
  max_daily_loss: 0.03          # More conservative daily loss
  max_leverage: 8.0             # Reduced leverage for stability
  max_portfolio_risk: 0.15      # More conservative portfolio risk
  max_position_size: 0.25       # Slightly reduced position size
  portfolio_exposure_limit: 0.7 # More conservative exposure
  stop_loss_pct: 0.015          # Tighter stop loss for scalping
  take_profit_pct: 0.03         # Smaller profit targets for scalping
rl:
  batch_size: 64
  clip_range: 0.2
  ent_coef: 0.01
  gae_lambda: 0.95
  gamma: 0.99
  learning_rate: 0.0003
  model_type: PPO
  n_epochs: 10
  n_steps: 2048
  total_timesteps: 100000
scanner:
  enabled: true
  metrics_weights:
    depth_score: 0.18           # Slightly reduced
    flow_score: 0.25           # Increased - flow critical for crypto
    spread_score: 0.22         # Slightly reduced
    tick_atr_score: 0.20       # Unchanged - volatility important
    volume_score: 0.15         # Reduced - less critical than flow
  min_score_improvement: 8     # Reduced for more opportunities
  mode: scalping
  preferred_symbols:           # Optimized for lower fees and volatility
  - DOGE/USDT:USDT            # Lower fees than BTC
  - ADA/USDT:USDT             # Good volatility, lower fees
  - MATIC/USDT:USDT           # Added - good for scalping
  - ATOM/USDT:USDT            # Added - decent volatility
  stability_threshold: 55      # Reduced for more opportunities
  symbols:
  - ETH/USDT:USDT             # Removed BTC (high fees)
  - DOGE/USDT:USDT
  - ADA/USDT:USDT
  - SOL/USDT:USDT
  - MATIC/USDT:USDT           # Added
  - ATOM/USDT:USDT            # Added
  - DOT/USDT:USDT             # Added
  update_interval: 4.0        # Faster updates for crypto
trading:
  auto_start_on_launch: false
  autonomous_mode: true
  cooldown_minutes: 20          # Reduced for more opportunities
  cycle_delay: 25               # Slightly faster cycles
  initial_balance: 10000.0
  max_positions: 2              # Reduced for better focus
  max_trades_per_day: 40        # Reduced for quality over quantity
  min_confidence: 0.58          # Increased for better accuracy
  trading_hours:
    end: 24
    start: 0
  use_rl: true
