"""
ScalperGPT - Advanced AI-Powered Scalping Analysis System
Provides high-frequency trading analysis with quality thresholds for autonomous trading
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import statistics

# Configure Unicode-safe logging
try:
    from core.unicode_safe_logging import configure_unicode_safe_logging
    configure_unicode_safe_logging()
except ImportError:
    pass

logger = logging.getLogger(__name__)

@dataclass
class ScalpingOpportunity:
    """Represents a scalping opportunity with quality metrics"""
    symbol: str
    direction: str  # 'LONG' or 'SHORT'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    spread_quality: float
    decision_quality: float
    liquidity_score: float
    volatility_score: float
    timestamp: datetime
    reasoning: str
    expected_duration: int  # seconds
    risk_reward_ratio: float

@dataclass
class MarketMicrostructure:
    """Market microstructure analysis for scalping"""
    symbol: str
    bid_ask_spread: float
    spread_pct: float
    order_book_imbalance: float
    tick_size: float
    volume_profile: Dict[str, float]
    price_momentum: float
    liquidity_depth: float
    market_impact: float
    timestamp: datetime

class ScalperGPT:
    """
    Advanced AI-powered scalping analysis system
    Provides real-time market microstructure analysis and scalping opportunities
    with strict quality thresholds for autonomous trading
    """
    
    def __init__(self, llm_provider=None, config: Dict[str, Any] = None):
        self.llm_provider = llm_provider
        self.config = config or self._get_default_config()

        # Quality thresholds (as specified in requirements)
        self.quality_thresholds = {
            'spread_quality': 7.0,      # Minimum spread quality score
            'decision_quality': 8.0,    # Minimum decision quality score
            'min_confidence': 0.75,     # Minimum confidence for execution
            'max_spread_pct': 0.15,     # Maximum spread percentage
            'min_liquidity': 1000,      # Minimum liquidity depth
            'min_risk_reward': 2.0      # Minimum risk/reward ratio
        }

        # Initialize advanced systems
        try:
            from .advanced_microstructure import AdvancedMicrostructureAnalyzer
            from .regime_detector import RegimeDetector
            from .volatility_pause_system import VolatilityPauseSystem
            from .prompt_optimizer import PromptOptimizer

            self.microstructure_analyzer = AdvancedMicrostructureAnalyzer()
            self.regime_detector = RegimeDetector()
            self.volatility_system = VolatilityPauseSystem()
            self.prompt_optimizer = PromptOptimizer()

            logger.info("Advanced ScalperGPT systems initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing advanced systems: {e}")
            # Fallback to basic systems
            self.microstructure_analyzer = None
            self.regime_detector = None
            self.volatility_system = None
            self.prompt_optimizer = None
        
        # Analysis state
        self.market_data_cache = {}
        self.opportunity_history = []
        self.performance_metrics = {
            'opportunities_generated': 0,
            'opportunities_above_threshold': 0,
            'avg_spread_quality': 0.0,
            'avg_decision_quality': 0.0,
            'success_rate': 0.0
        }
        
        # Real-time analysis components
        self.microstructure_analyzer = MarketMicrostructureAnalyzer()
        self.opportunity_detector = OpportunityDetector(self.quality_thresholds)
        
        logger.info("[TARGET] ScalperGPT initialized with quality thresholds: "
                   f"spread_quality >= {self.quality_thresholds['spread_quality']}, "
                   f"decision_quality >= {self.quality_thresholds['decision_quality']}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default ScalperGPT configuration"""
        return {
            'analysis_interval': 5,      # seconds
            'max_opportunities': 10,     # maximum concurrent opportunities
            'scalping_timeframe': '1m',  # primary timeframe
            'lookback_periods': 20,      # periods for analysis
            'volatility_window': 14,     # periods for volatility calculation
            'enable_ai_enhancement': True,
            'ai_prompt_template': 'scalping_analysis'
        }
    
    async def analyze_scalping_opportunity(self, symbol: str, market_data: Dict[str, Any]) -> Optional[ScalpingOpportunity]:
        """
        Analyze market data for scalping opportunities with quality scoring
        
        Args:
            symbol: Trading symbol
            market_data: Real-time market data
            
        Returns:
            ScalpingOpportunity if quality thresholds are met, None otherwise
        """
        try:
            start_time = time.time()

            # 1. Advanced volatility analysis and pause check
            if self.volatility_system:
                volatility_metrics = self.volatility_system.analyze_volatility(symbol, market_data)
                if volatility_metrics.pause_recommended:
                    logger.info(f"Trading paused due to high volatility: {volatility_metrics.volatility_state}")
                    return None

                # Get trading adjustments
                trading_adjustments = self.volatility_system.get_trading_adjustments(volatility_metrics)
                market_data['volatility_adjustments'] = trading_adjustments

            # 2. Advanced market microstructure analysis
            if self.microstructure_analyzer:
                microstructure_analysis = self.microstructure_analyzer.analyze_microstructure(symbol, market_data)
                if microstructure_analysis:
                    market_data['microstructure'] = microstructure_analysis

                    # Check execution quality threshold
                    if microstructure_analysis.execution_quality < 6.0:
                        logger.debug(f"Poor execution quality ({microstructure_analysis.execution_quality:.1f}), skipping opportunity")
                        return None

            # 3. Multi-timeframe regime detection
            if self.regime_detector:
                regime_analysis = self.regime_detector.detect_regime(symbol, market_data)
                if regime_analysis:
                    market_data['regime'] = regime_analysis

                    # Skip if regime suggests waiting
                    if regime_analysis.optimal_strategy == 'WAIT':
                        logger.debug(f"Regime analysis suggests waiting: {regime_analysis.primary_regime}")
                        return None

            # 4. Fallback to basic microstructure if advanced not available
            if not self.microstructure_analyzer:
                microstructure = await self.microstructure_analyzer.analyze(symbol, market_data)
                if not microstructure:
                    return None
                spread_quality = self._calculate_spread_quality(microstructure)
            else:
                microstructure = market_data.get('microstructure')
                spread_quality = microstructure.execution_quality if microstructure else 5.0

            # 5. Detect scalping opportunity with enhanced data
            opportunity = await self.opportunity_detector.detect_opportunity(
                symbol, market_data, microstructure
            )

            if not opportunity:
                return None

            # 6. Enhance with AI analysis using optimized prompts
            if self.config['enable_ai_enhancement'] and self.llm_provider:
                # Get best prompt template if optimizer available
                if self.prompt_optimizer:
                    market_context = {
                        'regime': regime_analysis.primary_regime.value if regime_analysis else 'UNKNOWN',
                        'volatility': volatility_metrics.atr_ratio if volatility_metrics else 1.0
                    }
                    best_prompt = self.prompt_optimizer.get_best_prompt_template(market_context)
                    if best_prompt:
                        # Use optimized prompt template
                        opportunity = await self._enhance_with_optimized_ai_analysis(opportunity, market_data, best_prompt)
                    else:
                        opportunity = await self._enhance_with_ai_analysis(opportunity, market_data)
                else:
                    opportunity = await self._enhance_with_ai_analysis(opportunity, market_data)
            
            # 5. Calculate decision quality score
            decision_quality = self._calculate_decision_quality(opportunity, microstructure)
            
            # 6. Apply quality thresholds
            if not self._meets_quality_thresholds(spread_quality, decision_quality, opportunity):
                logger.debug(f"🚫 {symbol} opportunity rejected: "
                           f"spread_quality={spread_quality:.1f}, "
                           f"decision_quality={decision_quality:.1f}")
                return None
            
            # 7. Create final opportunity with quality metrics
            scalping_opportunity = ScalpingOpportunity(
                symbol=symbol,
                direction=opportunity['direction'],
                entry_price=opportunity['entry_price'],
                target_price=opportunity['target_price'],
                stop_loss=opportunity['stop_loss'],
                confidence=opportunity['confidence'],
                spread_quality=spread_quality,
                decision_quality=decision_quality,
                liquidity_score=microstructure.liquidity_depth,
                volatility_score=opportunity.get('volatility_score', 0.0),
                timestamp=datetime.now(),
                reasoning=opportunity.get('reasoning', ''),
                expected_duration=opportunity.get('duration', 60),
                risk_reward_ratio=opportunity.get('risk_reward', 0.0)
            )
            
            # 8. Update metrics and cache
            self._update_performance_metrics(scalping_opportunity)
            self.opportunity_history.append(scalping_opportunity)
            
            analysis_time = time.time() - start_time
            logger.info(f"[OK] {symbol} scalping opportunity: {scalping_opportunity.direction} "
                       f"(spread_quality={spread_quality:.1f}, "
                       f"decision_quality={decision_quality:.1f}, "
                       f"confidence={scalping_opportunity.confidence:.1%}, "
                       f"analysis_time={analysis_time:.3f}s)")
            
            return scalping_opportunity
            
        except Exception as e:
            logger.error(f"[ERROR] Error analyzing scalping opportunity for {symbol}: {e}")
            return None
    
    def _calculate_spread_quality(self, microstructure: MarketMicrostructure) -> float:
        """
        Calculate spread quality score (0-10 scale)
        Higher score = better spread conditions for scalping
        """
        try:
            # Base score from spread percentage (lower is better)
            spread_score = max(0, 10 - (microstructure.spread_pct * 100))
            
            # Adjust for liquidity depth
            liquidity_factor = min(1.0, microstructure.liquidity_depth / 5000)
            
            # Adjust for order book balance
            balance_factor = 1.0 - abs(microstructure.order_book_imbalance) / 100
            
            # Composite spread quality score
            quality_score = spread_score * liquidity_factor * balance_factor
            
            return min(10.0, max(0.0, quality_score))
            
        except Exception as e:
            logger.error(f"Error calculating spread quality: {e}")
            return 0.0
    
    def _calculate_decision_quality(self, opportunity: Dict[str, Any], microstructure: MarketMicrostructure) -> float:
        """
        Calculate decision quality score (0-10 scale)
        Higher score = higher confidence in the trading decision
        """
        try:
            # Base score from opportunity confidence
            confidence_score = opportunity.get('confidence', 0.0) * 10
            
            # Technical analysis strength
            technical_score = opportunity.get('technical_strength', 0.5) * 10
            
            # Risk/reward ratio quality
            risk_reward = opportunity.get('risk_reward', 1.0)
            rr_score = min(10.0, risk_reward * 2)  # 2:1 ratio = 4 points, 5:1 = 10 points
            
            # Market momentum alignment
            momentum_score = abs(microstructure.price_momentum) * 10
            
            # Volatility appropriateness (moderate volatility is best for scalping)
            volatility = opportunity.get('volatility_score', 0.5)
            volatility_score = 10 * (1 - abs(volatility - 0.6))  # Optimal around 0.6
            
            # Weighted composite score - OPTIMIZED for crypto futures scalping
            weights = {
                'confidence': 0.25,      # Reduced - balance with other factors
                'technical': 0.22,       # Slightly reduced
                'risk_reward': 0.18,     # Slightly reduced
                'momentum': 0.25,        # Increased - momentum critical in crypto
                'volatility': 0.10       # Unchanged - volatility important but not dominant
            }
            
            decision_quality = (
                confidence_score * weights['confidence'] +
                technical_score * weights['technical'] +
                rr_score * weights['risk_reward'] +
                momentum_score * weights['momentum'] +
                volatility_score * weights['volatility']
            )
            
            return min(10.0, max(0.0, decision_quality))
            
        except Exception as e:
            logger.error(f"Error calculating decision quality: {e}")
            return 0.0
    
    def _meets_quality_thresholds(self, spread_quality: float, decision_quality: float, 
                                 opportunity: Dict[str, Any]) -> bool:
        """Check if opportunity meets all quality thresholds"""
        try:
            # Check spread quality threshold
            if spread_quality < self.quality_thresholds['spread_quality']:
                return False
            
            # Check decision quality threshold
            if decision_quality < self.quality_thresholds['decision_quality']:
                return False
            
            # Check confidence threshold
            if opportunity.get('confidence', 0.0) < self.quality_thresholds['min_confidence']:
                return False
            
            # Check risk/reward ratio
            if opportunity.get('risk_reward', 0.0) < self.quality_thresholds['min_risk_reward']:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking quality thresholds: {e}")
            return False
    
    async def _enhance_with_ai_analysis(self, opportunity: Dict[str, Any], 
                                      market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance opportunity analysis with AI/LLM insights"""
        try:
            if not self.llm_provider:
                return opportunity
            
            # Build AI prompt for scalping analysis
            prompt = self._build_scalping_prompt(opportunity, market_data)
            
            # Get AI analysis
            ai_response = await self.llm_provider.generate_response(prompt)
            
            if ai_response and ai_response.content:
                # Parse AI insights and enhance opportunity
                ai_insights = self._parse_ai_response(ai_response.content)
                opportunity.update(ai_insights)
            
            return opportunity
            
        except Exception as e:
            logger.error(f"Error enhancing with AI analysis: {e}")
            return opportunity

    async def _enhance_with_optimized_ai_analysis(self, opportunity: Dict[str, Any],
                                                market_data: Dict[str, Any],
                                                optimized_prompt: str) -> Dict[str, Any]:
        """Enhance opportunity analysis with optimized AI/LLM prompts"""
        try:
            if not self.llm_provider:
                return opportunity

            # Use the optimized prompt template
            ai_response = await self.llm_provider.generate_response(optimized_prompt)

            if ai_response and ai_response.content:
                # Parse AI insights and enhance opportunity
                ai_insights = self._parse_ai_response(ai_response.content)
                opportunity.update(ai_insights)

                # Log for prompt optimization if available
                if self.prompt_optimizer:
                    prompt_hash = self.prompt_optimizer.create_prompt_hash(optimized_prompt)
                    opportunity['prompt_hash'] = prompt_hash
                    opportunity['prompt_text'] = optimized_prompt

            return opportunity

        except Exception as e:
            logger.error(f"Error enhancing with optimized AI analysis: {e}")
            return opportunity
    
    def _build_scalping_prompt(self, opportunity: Dict[str, Any], market_data: Dict[str, Any]) -> str:
        """Build advanced AI prompt for crypto futures scalping analysis"""

        # Extract advanced market metrics
        orderbook_imbalance = market_data.get('orderbook_imbalance', 0.0)
        spread_quality = market_data.get('spread_quality', 5.0)
        liquidity_depth = market_data.get('liquidity_depth', 1000.0)
        market_impact = market_data.get('market_impact', 0.01)
        momentum_1m = market_data.get('momentum_1m', 0.0)
        volume_momentum = market_data.get('volume_momentum', 1.0)
        tick_velocity = market_data.get('tick_velocity', 0.0)
        volatility_regime = market_data.get('volatility_regime', 'NORMAL')
        atr_14 = market_data.get('atr_14', 0.001)

        return f"""🚀 CRYPTO FUTURES SCALPING SPECIALIST

MARKET MICROSTRUCTURE:
- Orderbook Imbalance: {orderbook_imbalance:.2f}%
- Spread Quality: {spread_quality:.1f}/10
- Liquidity Depth: ${liquidity_depth:,.0f}
- Market Impact: {market_impact:.4f}%

MOMENTUM ANALYSIS:
- Price Momentum (1m): {momentum_1m:.3f}%
- Volume Momentum: {volume_momentum:.2f}x
- Tick Velocity: {tick_velocity:.1f} ticks/min
- Volatility Regime: {volatility_regime}

OPPORTUNITY DETAILS:
- Symbol: {opportunity.get('symbol', 'Unknown')}
- Direction: {opportunity.get('direction', 'Unknown')}
- Entry Price: {opportunity.get('entry_price', 0):.6f}
- Current Price: {market_data.get('price', 0):.6f}
- Spread: {market_data.get('spread', 0):.6f}

DECISION REQUIRED:
Analyze this crypto futures scalping opportunity and provide:

JSON FORMAT ONLY:
{{"technical_strength":0.85,"risk_assessment":"LOW","timing_quality":"EXCELLENT","confidence":78,"entry_timing":"IMMEDIATE","reasoning":"Strong momentum + orderbook imbalance + tight spread"}}

Focus on: Technical strength (0-1), Risk level (LOW/MEDIUM/HIGH), Timing (POOR/FAIR/GOOD/EXCELLENT), Confidence (60-95%), Entry timing (IMMEDIATE/WAIT_DIP/WAIT_BREAKOUT)
        
        Format: technical_strength:X.X|risk_level:LOW/MED/HIGH|timing:GOOD/FAIR/POOR|reasoning:brief explanation
        """
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response into structured insights"""
        insights = {}
        try:
            parts = response.split('|')
            for part in parts:
                if ':' in part:
                    key, value = part.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key == 'technical_strength':
                        insights['technical_strength'] = float(value)
                    elif key == 'reasoning':
                        insights['reasoning'] = value
                    elif key == 'risk_level':
                        insights['risk_level'] = value
                    elif key == 'timing':
                        insights['timing_quality'] = value
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
        
        return insights
    
    def _update_performance_metrics(self, opportunity: ScalpingOpportunity):
        """Update performance metrics"""
        self.performance_metrics['opportunities_generated'] += 1
        
        if (opportunity.spread_quality >= self.quality_thresholds['spread_quality'] and
            opportunity.decision_quality >= self.quality_thresholds['decision_quality']):
            self.performance_metrics['opportunities_above_threshold'] += 1
        
        # Update running averages
        total_ops = len(self.opportunity_history)
        if total_ops > 0:
            self.performance_metrics['avg_spread_quality'] = statistics.mean(
                [op.spread_quality for op in self.opportunity_history[-100:]]  # Last 100
            )
            self.performance_metrics['avg_decision_quality'] = statistics.mean(
                [op.decision_quality for op in self.opportunity_history[-100:]]  # Last 100
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.performance_metrics,
            'quality_thresholds': self.quality_thresholds,
            'recent_opportunities': len([
                op for op in self.opportunity_history 
                if (datetime.now() - op.timestamp).seconds < 300  # Last 5 minutes
            ])
        }


class MarketMicrostructureAnalyzer:
    """Analyzes market microstructure for scalping opportunities"""
    
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> Optional[MarketMicrostructure]:
        """Analyze market microstructure"""
        try:
            # Extract order book data
            orderbook = market_data.get('orderbook', {})
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            
            if not bids or not asks:
                return None
            
            # Calculate spread metrics
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_pct = (spread / best_ask) * 100
            
            # Calculate order book imbalance
            bid_volume = sum([bid[1] for bid in bids[:5]])
            ask_volume = sum([ask[1] for ask in asks[:5]])
            total_volume = bid_volume + ask_volume
            imbalance = ((bid_volume - ask_volume) / total_volume * 100) if total_volume > 0 else 0
            
            # Calculate liquidity depth
            liquidity_depth = bid_volume + ask_volume
            
            # Estimate market impact (simplified)
            market_impact = spread_pct * 0.5  # Rough estimate
            
            return MarketMicrostructure(
                symbol=symbol,
                bid_ask_spread=spread,
                spread_pct=spread_pct,
                order_book_imbalance=imbalance,
                tick_size=market_data.get('tick_size', 0.00001),
                volume_profile=market_data.get('volume_profile', {}),
                price_momentum=market_data.get('momentum', 0.0),
                liquidity_depth=liquidity_depth,
                market_impact=market_impact,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error analyzing microstructure for {symbol}: {e}")
            return None


class OpportunityDetector:
    """Detects scalping opportunities based on market conditions"""
    
    def __init__(self, quality_thresholds: Dict[str, float]):
        self.quality_thresholds = quality_thresholds
    
    async def detect_opportunity(self, symbol: str, market_data: Dict[str, Any], 
                               microstructure: MarketMicrostructure) -> Optional[Dict[str, Any]]:
        """Detect scalping opportunity"""
        try:
            current_price = market_data.get('price', 0)
            if not current_price:
                return None
            
            # Check if spread is acceptable for scalping
            if microstructure.spread_pct > self.quality_thresholds['max_spread_pct']:
                return None
            
            # Advanced multi-factor opportunity detection
            momentum = market_data.get('momentum', 0.0)
            volatility = market_data.get('volatility', 0.0)
            volume_momentum = market_data.get('volume_momentum', 1.0)
            orderbook_imbalance = microstructure.order_book_imbalance

            # Multi-factor opportunity scoring
            momentum_score = min(1.0, abs(momentum) / 0.5)  # Normalize to 0-1
            volume_score = min(1.0, abs(volume_momentum - 1.0) * 2)  # Volume deviation
            imbalance_score = min(1.0, abs(orderbook_imbalance) / 20.0)  # Orderbook imbalance
            spread_score = 1.0 - min(1.0, microstructure.spread_pct / 0.2)  # Spread quality

            # Composite opportunity score
            opportunity_score = (
                momentum_score * 0.35 +
                volume_score * 0.25 +
                imbalance_score * 0.25 +
                spread_score * 0.15
            )

            # Require minimum opportunity score
            if opportunity_score < 0.4:  # 40% minimum opportunity threshold
                return None
            
            # Determine direction
            direction = 'LONG' if momentum > 0 else 'SHORT'
            
            # Calculate entry, target, and stop loss
            if direction == 'LONG':
                entry_price = current_price * 1.0005  # Slightly above current
                target_price = current_price * 1.01   # 1% target
                stop_loss = current_price * 0.995     # 0.5% stop
            else:
                entry_price = current_price * 0.9995  # Slightly below current
                target_price = current_price * 0.99   # 1% target
                stop_loss = current_price * 1.005     # 0.5% stop
            
            # Calculate risk/reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            # Advanced confidence calculation using opportunity score and market conditions
            base_confidence = opportunity_score * 0.6  # Base from opportunity quality

            # Liquidity confidence boost
            liquidity_factor = min(0.2, microstructure.liquidity_depth / 10000 * 0.2)

            # Volatility adjustment (moderate volatility is best for scalping)
            vol_optimal = 0.15  # Optimal volatility for scalping
            vol_penalty = abs(volatility - vol_optimal) / vol_optimal * 0.1
            vol_factor = max(0, 0.15 - vol_penalty)

            # Market impact penalty
            impact_penalty = min(0.05, microstructure.market_impact * 10)

            # Final confidence calculation
            confidence = min(0.95, max(0.5,
                base_confidence + liquidity_factor + vol_factor - impact_penalty
            ))
            
            return {
                'symbol': symbol,
                'direction': direction,
                'entry_price': entry_price,
                'target_price': target_price,
                'stop_loss': stop_loss,
                'confidence': confidence,
                'risk_reward': risk_reward,
                'volatility_score': volatility,
                'duration': 30,  # Expected 60 seconds
                'reasoning': f'{direction} momentum scalp, {momentum:.3f} momentum'
            }
            
        except Exception as e:
            logger.error(f"Error detecting opportunity for {symbol}: {e}")
            return None
