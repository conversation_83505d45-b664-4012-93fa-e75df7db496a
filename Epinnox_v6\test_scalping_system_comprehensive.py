#!/usr/bin/env python3
"""
🚀 COMPREHENSIVE SCALPING SYSTEM VALIDATION
End-to-end testing of DOGE scalping with autonomous decision-making
"""

import sys
import os
import time
import logging
import asyncio
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_scalping_system_comprehensive():
    """Comprehensive scalping system validation"""
    print("🚀 COMPREHENSIVE SCALPING SYSTEM VALIDATION")
    print("=" * 70)
    
    test_results = {
        'data_flow': False,
        'order_execution': False,
        'long_trades': False,
        'short_trades': False,
        'decision_pipeline': False,
        'liquidity_validation': False,
        'performance_metrics': False
    }
    
    try:
        # Test 1: DOGE Data Flow Validation
        print("\n1️⃣ Testing DOGE Data Flow...")
        
        from data.live_data_manager import LiveDataManager
        
        live_data_manager = LiveDataManager()
        test_symbol = "DOGE/USDT:USDT"
        
        # Subscribe to DOGE data
        live_data_manager.subscribe_symbol(test_symbol, ['1m', '5m'])
        time.sleep(3)  # Allow subscription time
        
        # Test data retrieval
        candles_1m = live_data_manager.get_chart_data(test_symbol, '1m', limit=50)
        candles_5m = live_data_manager.get_chart_data(test_symbol, '5m', limit=20)
        recent_trades = live_data_manager.get_recent_trades(test_symbol, limit=100)
        orderbook = live_data_manager.get_latest_orderbook(test_symbol)
        
        data_quality = {
            '1m_candles': len(candles_1m) if candles_1m else 0,
            '5m_candles': len(candles_5m) if candles_5m else 0,
            'recent_trades': len(recent_trades) if recent_trades else 0,
            'orderbook_bids': len(orderbook.get('bids', [])) if orderbook else 0,
            'orderbook_asks': len(orderbook.get('asks', [])) if orderbook else 0
        }
        
        print(f"  📊 Data Quality: {data_quality}")
        
        # Calculate data flow score
        data_score = 0
        if data_quality['1m_candles'] >= 30: data_score += 20
        if data_quality['5m_candles'] >= 15: data_score += 20
        if data_quality['recent_trades'] >= 50: data_score += 20
        if data_quality['orderbook_bids'] >= 10: data_score += 20
        if data_quality['orderbook_asks'] >= 10: data_score += 20
        
        test_results['data_flow'] = data_score >= 80
        print(f"  🎯 Data Flow Score: {data_score}/100 {'✅' if test_results['data_flow'] else '❌'}")
        
        # Test 2: Liquidity Validation with New Thresholds
        print("\n2️⃣ Testing Liquidity Validation...")
        
        from trading.intelligent_limit_order_manager import IntelligentLimitOrderManager
        
        # Create mock components for testing
        class MockTradingEngine:
            def place_order(self, *args, **kwargs):
                return {'status': 'success', 'order_id': f'test_{int(time.time())}'}

            def cancel_order(self, *args, **kwargs):
                return {'status': 'success'}

        class MockLiveDataManagerForOrder:
            def get_latest_orderbook(self, symbol):
                return orderbook if orderbook else {
                    'symbol': symbol,
                    'bids': [[0.4, 0.3], [0.39, 0.5], [0.38, 1.0]],
                    'asks': [[0.41, 1.6], [0.42, 2.0], [0.43, 1.5]]
                }

        # Initialize order manager with correct signature
        order_manager = IntelligentLimitOrderManager(
            MockTradingEngine(),
            MockLiveDataManagerForOrder()
        )
        
        # Test with realistic DOGE liquidity
        test_orderbook = {
            'symbol': test_symbol,
            'bids': [[0.4, 0.3], [0.39, 0.5], [0.38, 1.0]],  # Low but realistic liquidity
            'asks': [[0.41, 1.6], [0.42, 2.0], [0.43, 1.5]]
        }
        
        liquidity_valid = order_manager._validate_order_book(test_orderbook)
        threshold = order_manager.config.get('min_order_book_depth', 50)
        
        test_results['liquidity_validation'] = liquidity_valid
        print(f"  📊 Liquidity Threshold: {threshold}")
        print(f"  🎯 Liquidity Validation: {'✅ PASSED' if liquidity_valid else '❌ FAILED'}")
        
        # Test 3: Order Execution Pipeline
        print("\n3️⃣ Testing Order Execution Pipeline...")
        
        execution_tests = []
        
        # Test LONG order
        try:
            long_order = order_manager.place_smart_limit_order(
                symbol=test_symbol,
                side='buy',
                amount=100.0,  # Small test amount
                confidence=85.0
            )
            execution_tests.append(('LONG', long_order is not None))
            test_results['long_trades'] = long_order is not None
        except Exception as e:
            execution_tests.append(('LONG', False))
            print(f"    ❌ LONG order error: {e}")
        
        # Test SHORT order
        try:
            short_order = order_manager.place_smart_limit_order(
                symbol=test_symbol,
                side='sell',
                amount=100.0,  # Small test amount
                confidence=85.0
            )
            execution_tests.append(('SHORT', short_order is not None))
            test_results['short_trades'] = short_order is not None
        except Exception as e:
            execution_tests.append(('SHORT', False))
            print(f"    ❌ SHORT order error: {e}")
        
        test_results['order_execution'] = all(result for _, result in execution_tests)
        
        for side, success in execution_tests:
            print(f"  🎯 {side} Order: {'✅ SUCCESS' if success else '❌ FAILED'}")
        
        # Test 4: Autonomous Decision-Making Pipeline
        print("\n4️⃣ Testing Autonomous Decision-Making...")
        
        from core.llm_orchestrator import LLMPromptOrchestrator, LmStudioRunner
        
        # Initialize orchestrator
        lmstudio_runner = LmStudioRunner()
        orchestrator = LLMPromptOrchestrator(lmstudio_runner)
        
        # Create comprehensive market data
        market_data = {
            'symbol': test_symbol,
            'candles_1m': candles_1m,
            'candles_5m': candles_5m,
            'recent_trades': recent_trades,
            'orderbook': orderbook,
            'volume_24h': 1000000,
            'price': 0.4 if orderbook else 0.4,
            'best_bid': orderbook.get('bids', [[0.4]])[0][0] if orderbook else 0.4,
            'best_ask': orderbook.get('asks', [[0.41]])[0][0] if orderbook else 0.41
        }
        
        # Test decision scenarios
        decision_tests = []
        
        # Test 1: Bullish scenario (should favor LONG)
        bullish_results = {
            'risk_assessment': type('MockResult', (), {
                'response': {'APPROVED': True, 'CONFIDENCE': 90},
                'success': True
            })(),
            'entry_timing': type('MockResult', (), {
                'response': {'ACTION': 'ENTER_NOW', 'DIRECTION': 'LONG', 'CONFIDENCE': 85},
                'success': True
            })(),
            'opportunity_scanner': type('MockResult', (), {
                'response': {'BEST_OPPORTUNITY': 'BULLISH_BREAKOUT', 'DECISION': 'LONG', 'CONFIDENCE': 80},
                'success': True
            })()
        }
        
        bullish_decision = orchestrator.get_aggregated_decision(bullish_results)
        decision_tests.append(('BULLISH', bullish_decision.decision.value == 'LONG'))
        
        # Test 2: Bearish scenario (should favor SHORT)
        bearish_results = {
            'risk_assessment': type('MockResult', (), {
                'response': {'APPROVED': True, 'CONFIDENCE': 85},
                'success': True
            })(),
            'entry_timing': type('MockResult', (), {
                'response': {'ACTION': 'ENTER_NOW', 'DIRECTION': 'SHORT', 'CONFIDENCE': 90},
                'success': True
            })(),
            'opportunity_scanner': type('MockResult', (), {
                'response': {'BEST_OPPORTUNITY': 'BEARISH_BREAKDOWN', 'DECISION': 'SHORT', 'CONFIDENCE': 85},
                'success': True
            })()
        }
        
        bearish_decision = orchestrator.get_aggregated_decision(bearish_results)
        decision_tests.append(('BEARISH', bearish_decision.decision.value == 'SHORT'))
        
        test_results['decision_pipeline'] = all(result for _, result in decision_tests)
        
        for scenario, success in decision_tests:
            print(f"  🎯 {scenario} Decision: {'✅ CORRECT' if success else '❌ INCORRECT'}")
        
        # Test 5: Performance Metrics Calculation
        print("\n5️⃣ Testing Performance Metrics...")
        
        # Test advanced analysis systems
        from core.regime_detector import RegimeDetector
        from core.volatility_pause_system import VolatilityPauseSystem
        from core.advanced_microstructure import AdvancedMicrostructureAnalyzer
        
        regime_detector = RegimeDetector()
        volatility_system = VolatilityPauseSystem()
        microstructure_analyzer = AdvancedMicrostructureAnalyzer()
        
        analysis_results = {}
        
        # Test regime detection
        try:
            regime_result = regime_detector.detect_regime(test_symbol, market_data)
            analysis_results['regime'] = regime_result is not None
        except Exception as e:
            analysis_results['regime'] = False
            print(f"    ⚠️ Regime detection error: {e}")
        
        # Test volatility analysis
        try:
            volatility_result = volatility_system.analyze_volatility(test_symbol, market_data)
            analysis_results['volatility'] = volatility_result is not None
        except Exception as e:
            analysis_results['volatility'] = False
            print(f"    ⚠️ Volatility analysis error: {e}")
        
        # Test microstructure analysis
        try:
            microstructure_result = microstructure_analyzer.analyze_microstructure(test_symbol, market_data)
            analysis_results['microstructure'] = microstructure_result is not None
        except Exception as e:
            analysis_results['microstructure'] = False
            print(f"    ⚠️ Microstructure analysis error: {e}")
        
        performance_score = sum(analysis_results.values()) / len(analysis_results) * 100
        test_results['performance_metrics'] = performance_score >= 66  # At least 2/3 working
        
        for system, working in analysis_results.items():
            print(f"  🎯 {system.title()} Analysis: {'✅ WORKING' if working else '❌ FAILED'}")
        
        print(f"  📊 Performance Score: {performance_score:.1f}%")
        
        # Final Assessment
        print(f"\n{'='*70}")
        print("🚀 COMPREHENSIVE SCALPING SYSTEM VALIDATION SUMMARY")
        print("="*70)
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"\n📊 TEST RESULTS:")
        for test_name, passed in test_results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests} tests passed)")
        
        # Production Readiness Assessment
        if success_rate >= 85:
            print("\n🚀 SYSTEM READY FOR PRODUCTION DEPLOYMENT")
            print("✅ All critical systems operational")
            print("✅ DOGE scalping fully functional")
            print("✅ Bidirectional trading confirmed")
            print("✅ Order execution pipeline working")
            
            print("\n📋 PRODUCTION DEPLOYMENT RECOMMENDATIONS:")
            print("1. Start with ultra-conservative position sizing (1% of balance)")
            print("2. Monitor first 10 trades closely for execution quality")
            print("3. Verify both LONG and SHORT trades execute successfully")
            print("4. Track performance metrics for 24-48 hours")
            print("5. Gradually increase position size based on performance")
            
        elif success_rate >= 70:
            print("\n⚠️ SYSTEM MOSTLY READY - MINOR ISSUES TO ADDRESS")
            print("🔧 Some components need attention before full deployment")
            
        else:
            print("\n❌ SYSTEM NOT READY FOR PRODUCTION")
            print("🚨 Critical issues must be resolved before deployment")
        
        print(f"\nValidation completed at: {datetime.now()}")
        return success_rate >= 85
        
    except Exception as e:
        print(f"❌ Critical error in scalping system validation: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_scalping_system_comprehensive()
    sys.exit(0 if success else 1)
