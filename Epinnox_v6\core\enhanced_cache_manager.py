#!/usr/bin/env python3
"""
Enhanced Cache Manager for Critical Trading Operations
Resolves stale data issues in high-frequency scalping scenarios
"""

import time
import threading
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class CacheMode(Enum):
    NORMAL = "normal"
    SCALPING = "scalping"
    EMERGENCY = "emergency"

@dataclass
class CacheEntry:
    """Enhanced cache entry with metadata"""
    data: Any
    timestamp: float
    symbol: Optional[str] = None
    mode: CacheMode = CacheMode.NORMAL
    access_count: int = 0
    ttl_override: Optional[float] = None
    last_access: float = field(default_factory=time.time)
    
    @property
    def age(self) -> float:
        return time.time() - self.timestamp
    
    @property
    def effective_ttl(self) -> float:
        if self.ttl_override:
            return self.ttl_override
        
        # Dynamic TTL based on mode
        if self.mode == CacheMode.EMERGENCY:
            return 1.0  # 1 second for emergency mode
        elif self.mode == CacheMode.SCALPING:
            return 2.0  # 2 seconds for scalping
        else:
            return 30.0  # 30 seconds for normal mode
    
    @property
    def is_valid(self) -> bool:
        return self.age <= self.effective_ttl
    
    def access(self):
        """Record cache access"""
        self.access_count += 1
        self.last_access = time.time()

class EnhancedCacheManager:
    """
    Advanced cache manager for trading operations with:
    - Symbol-aware caching
    - Mode-based TTL adjustment
    - Real-time invalidation
    - Performance monitoring
    """
    
    def __init__(self):
        self.caches: Dict[str, Dict[str, CacheEntry]] = {}
        self.lock = threading.RLock()
        self.mode = CacheMode.NORMAL
        self.symbol_context = None
        
        # Performance metrics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'invalidations': 0,
            'forced_refreshes': 0,
            'stale_data_prevented': 0
        }
        
        # Cache categories with different behaviors
        self.cache_categories = {
            'market_data': {'max_entries': 50, 'cleanup_threshold': 100},
            'positions': {'max_entries': 20, 'cleanup_threshold': 40},
            'orders': {'max_entries': 100, 'cleanup_threshold': 200},
            'balance': {'max_entries': 5, 'cleanup_threshold': 10},
            'orderbook': {'max_entries': 30, 'cleanup_threshold': 60}
        }
        
        logger.info("Enhanced Cache Manager initialized for critical trading operations")
    
    def set_mode(self, mode: CacheMode, symbol: Optional[str] = None):
        """Set cache mode and symbol context"""
        with self.lock:
            old_mode = self.mode
            old_symbol = self.symbol_context
            
            self.mode = mode
            self.symbol_context = symbol
            
            logger.info(f"Cache mode changed: {old_mode.value} → {mode.value}, symbol: {old_symbol} → {symbol}")
            
            # If symbol changed during scalping, invalidate relevant caches
            if mode == CacheMode.SCALPING and old_symbol != symbol and symbol is not None:
                self.invalidate_symbol_caches(symbol)
    
    def put(self, category: str, key: str, data: Any, symbol: Optional[str] = None, ttl_override: Optional[float] = None) -> bool:
        """Store data in cache with enhanced metadata"""
        try:
            with self.lock:
                if category not in self.caches:
                    self.caches[category] = {}
                
                # Create cache key (include symbol if provided)
                cache_key = f"{symbol}:{key}" if symbol else key
                
                entry = CacheEntry(
                    data=data,
                    timestamp=time.time(),
                    symbol=symbol,
                    mode=self.mode,
                    ttl_override=ttl_override
                )
                
                self.caches[category][cache_key] = entry
                
                # Cleanup if needed
                self._cleanup_cache_category(category)
                
                logger.debug(f"Cached {category}:{cache_key} (mode: {self.mode.value}, TTL: {entry.effective_ttl}s)")
                return True
                
        except Exception as e:
            logger.error(f"Error putting data in cache: {e}")
            return False
    
    def get(self, category: str, key: str, symbol: Optional[str] = None, max_age: Optional[float] = None) -> Optional[Any]:
        """Retrieve data from cache with validation"""
        try:
            with self.lock:
                if category not in self.caches:
                    self.stats['misses'] += 1
                    return None
                
                # Try symbol-specific key first, then fallback to general key
                cache_keys = []
                if symbol:
                    cache_keys.append(f"{symbol}:{key}")
                cache_keys.append(key)
                
                for cache_key in cache_keys:
                    if cache_key in self.caches[category]:
                        entry = self.caches[category][cache_key]
                        
                        # Check if entry is still valid
                        effective_max_age = max_age if max_age is not None else entry.effective_ttl
                        
                        if entry.age <= effective_max_age:
                            # Valid cache hit
                            entry.access()
                            self.stats['hits'] += 1
                            
                            logger.debug(f"Cache hit: {category}:{cache_key} (age: {entry.age:.2f}s)")
                            return entry.data
                        else:
                            # Stale data detected
                            logger.warning(f"Stale cache entry: {category}:{cache_key} (age: {entry.age:.2f}s > max: {effective_max_age:.2f}s)")
                            self.stats['stale_data_prevented'] += 1
                            
                            # Remove stale entry
                            del self.caches[category][cache_key]
                
                self.stats['misses'] += 1
                return None
                
        except Exception as e:
            logger.error(f"Error getting data from cache: {e}")
            self.stats['misses'] += 1
            return None
    
    def invalidate(self, category: str, key: str = None, symbol: Optional[str] = None) -> int:
        """Invalidate cache entries"""
        try:
            with self.lock:
                invalidated = 0
                
                if category not in self.caches:
                    return 0
                
                if key is None:
                    # Invalidate entire category
                    if symbol:
                        # Invalidate only entries for specific symbol
                        keys_to_remove = [k for k in self.caches[category].keys() 
                                        if k.startswith(f"{symbol}:") or 
                                        (self.caches[category][k].symbol == symbol)]
                    else:
                        # Invalidate all entries in category
                        keys_to_remove = list(self.caches[category].keys())
                    
                    for k in keys_to_remove:
                        del self.caches[category][k]
                        invalidated += 1
                else:
                    # Invalidate specific key
                    cache_keys = []
                    if symbol:
                        cache_keys.append(f"{symbol}:{key}")
                    cache_keys.append(key)
                    
                    for cache_key in cache_keys:
                        if cache_key in self.caches[category]:
                            del self.caches[category][cache_key]
                            invalidated += 1
                
                self.stats['invalidations'] += invalidated
                logger.info(f"Invalidated {invalidated} entries from {category} (symbol: {symbol}, key: {key})")
                return invalidated
                
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")
            return 0
    
    def invalidate_symbol_caches(self, symbol: str) -> int:
        """Invalidate all caches related to a specific symbol"""
        total_invalidated = 0
        
        for category in self.caches.keys():
            invalidated = self.invalidate(category, symbol=symbol)
            total_invalidated += invalidated
        
        logger.info(f"Invalidated {total_invalidated} total entries for symbol {symbol}")
        return total_invalidated
    
    def force_refresh_trigger(self, category: str, key: str, symbol: Optional[str] = None):
        """Trigger forced refresh by invalidating and logging the action"""
        invalidated = self.invalidate(category, key, symbol)
        self.stats['forced_refreshes'] += 1
        
        logger.warning(f"FORCED REFRESH triggered for {category}:{key} (symbol: {symbol})")
        return invalidated > 0
    
    def is_stale(self, category: str, key: str, symbol: Optional[str] = None, max_age: float = None) -> bool:
        """Check if cache entry is stale without accessing it"""
        try:
            with self.lock:
                if category not in self.caches:
                    return True
                
                cache_keys = []
                if symbol:
                    cache_keys.append(f"{symbol}:{key}")
                cache_keys.append(key)
                
                for cache_key in cache_keys:
                    if cache_key in self.caches[category]:
                        entry = self.caches[category][cache_key]
                        effective_max_age = max_age if max_age is not None else entry.effective_ttl
                        
                        return entry.age > effective_max_age
                
                return True  # Not found = stale
                
        except Exception as e:
            logger.error(f"Error checking staleness: {e}")
            return True
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        with self.lock:
            total_entries = sum(len(cache) for cache in self.caches.values())
            hit_rate = self.stats['hits'] / (self.stats['hits'] + self.stats['misses']) if (self.stats['hits'] + self.stats['misses']) > 0 else 0
            
            category_stats = {}
            for category, cache in self.caches.items():
                if cache:
                    ages = [entry.age for entry in cache.values()]
                    access_counts = [entry.access_count for entry in cache.values()]
                    
                    category_stats[category] = {
                        'entries': len(cache),
                        'avg_age': sum(ages) / len(ages) if ages else 0,
                        'max_age': max(ages) if ages else 0,
                        'avg_access_count': sum(access_counts) / len(access_counts) if access_counts else 0
                    }
                else:
                    category_stats[category] = {'entries': 0, 'avg_age': 0, 'max_age': 0, 'avg_access_count': 0}
            
            return {
                'mode': self.mode.value,
                'symbol_context': self.symbol_context,
                'total_entries': total_entries,
                'hit_rate': hit_rate,
                'stats': self.stats.copy(),
                'categories': category_stats
            }
    
    def _cleanup_cache_category(self, category: str):
        """Clean up cache category when it gets too large"""
        config = self.cache_categories.get(category, {'max_entries': 50, 'cleanup_threshold': 100})
        
        if len(self.caches[category]) > config['cleanup_threshold']:
            # Remove oldest entries
            entries_to_remove = len(self.caches[category]) - config['max_entries']
            
            # Sort by last access time (least recently used first)
            sorted_entries = sorted(
                self.caches[category].items(),
                key=lambda x: x[1].last_access
            )
            
            for i in range(entries_to_remove):
                key = sorted_entries[i][0]
                del self.caches[category][key]
            
            logger.debug(f"Cleaned up {entries_to_remove} entries from {category} cache")
    
    def emergency_clear_all(self):
        """Emergency cache clear for critical situations"""
        with self.lock:
            total_cleared = sum(len(cache) for cache in self.caches.values())
            self.caches.clear()
            
            logger.critical(f"EMERGENCY CACHE CLEAR: Removed {total_cleared} entries")
            return total_cleared

# Global cache manager instance
cache_manager = EnhancedCacheManager()

# Convenience functions for backward compatibility
def set_scalping_mode(symbol: str):
    """Enable scalping mode for a symbol"""
    cache_manager.set_mode(CacheMode.SCALPING, symbol)

def set_normal_mode():
    """Disable scalping mode"""
    cache_manager.set_mode(CacheMode.NORMAL)

def invalidate_symbol_cache(symbol: str) -> int:
    """Invalidate all cache entries for a symbol"""
    return cache_manager.invalidate_symbol_caches(symbol)

def force_fresh_data_fetch(category: str, key: str, symbol: str = None):
    """Force fresh data fetch by invalidating cache"""
    return cache_manager.force_refresh_trigger(category, key, symbol)
