#!/usr/bin/env python3
"""
🚨 EMERGENCY PEPE TRADING TEST
Test PEPE trading with current balance to resolve execution issues
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pepe_trading():
    """Test PEPE trading execution with current balance"""
    print("🚨 EMERGENCY PEPE TRADING TEST")
    print("=" * 60)
    
    try:
        # Initialize data manager for PEPE
        print("\n1️⃣ Initializing PEPE Data...")
        
        from data.live_data_manager import LiveDataManager
        
        live_data_manager = LiveDataManager()
        pepe_symbol = "PEPE/USDT:USDT"
        
        # Subscribe to PEPE data
        live_data_manager.subscribe_symbol(pepe_symbol, ['1m'])
        time.sleep(3)
        
        # Get PEPE market data
        candles = live_data_manager.get_chart_data(pepe_symbol, '1m', limit=10)
        orderbook = live_data_manager.get_latest_orderbook(pepe_symbol)
        trades = live_data_manager.get_recent_trades(pepe_symbol, limit=10)
        
        print(f"  📊 PEPE Data Quality:")
        print(f"    Candles: {len(candles) if candles else 0}")
        print(f"    Orderbook: {'✅ Valid' if orderbook and orderbook.get('bids') else '❌ Invalid'}")
        print(f"    Trades: {len(trades) if trades else 0}")
        
        if not (candles and orderbook and trades):
            print("  ❌ Insufficient PEPE data - aborting test")
            return False
        
        # Get current PEPE price
        current_price = float(orderbook['asks'][0][0]) if orderbook.get('asks') else 0.000008
        print(f"  💰 PEPE Price: ${current_price:.8f}")
        
        # Test position sizing
        print("\n2️⃣ Testing PEPE Position Sizing...")
        
        balance = 19.51
        position_pct = 0.80  # 80% of balance
        position_value = balance * position_pct
        
        # Calculate PEPE contracts
        pepe_contracts = int(position_value / current_price)
        min_pepe_contracts = 1000000  # 1M PEPE minimum
        
        if pepe_contracts < min_pepe_contracts:
            pepe_contracts = min_pepe_contracts
            actual_position_value = pepe_contracts * current_price
        else:
            actual_position_value = position_value
        
        print(f"  💰 Balance: ${balance:.2f}")
        print(f"  🎯 Target Position: ${position_value:.2f} (80%)")
        print(f"  📊 PEPE Contracts: {pepe_contracts:,}")
        print(f"  💵 Actual Value: ${actual_position_value:.2f}")
        print(f"  📏 Min Required: {min_pepe_contracts:,} contracts")
        
        if actual_position_value <= balance:
            print(f"  ✅ Position affordable with current balance")
        else:
            print(f"  ❌ Position exceeds balance")
            return False
        
        # Test order validation
        print("\n3️⃣ Testing Order Validation...")
        
        from trading.intelligent_limit_order_manager import IntelligentLimitOrderManager
        from trading.trading_system_interface import TradingSystemInterface
        
        # Initialize trading components
        trading_interface = TradingSystemInterface()
        order_manager = IntelligentLimitOrderManager(trading_interface, live_data_manager)
        
        # Test order book validation
        validation_result = order_manager._validate_order_book(orderbook)
        print(f"  📊 Order Book Validation: {'✅ PASSED' if validation_result else '❌ FAILED'}")
        
        if not validation_result:
            print("  ❌ Order book validation failed")
            return False
        
        # Test minimum order size validation
        print("\n4️⃣ Testing Minimum Order Size...")
        
        # Check if trading interface has validation method
        if hasattr(trading_interface, 'validate_and_adjust_minimum_order_size'):
            valid, adjusted_quantity = trading_interface.validate_and_adjust_minimum_order_size(
                pepe_symbol, pepe_contracts
            )
            print(f"  📊 Original Quantity: {pepe_contracts:,}")
            print(f"  📊 Adjusted Quantity: {adjusted_quantity:,}")
            print(f"  📊 Validation: {'✅ PASSED' if valid else '❌ FAILED'}")
            
            if valid:
                final_quantity = adjusted_quantity
            else:
                print("  ❌ Minimum order validation failed")
                return False
        else:
            final_quantity = pepe_contracts
            print(f"  ⚠️ No validation method available, using: {final_quantity:,}")
        
        # Calculate final trade parameters
        print("\n5️⃣ Final Trade Parameters...")
        
        final_value = final_quantity * current_price
        stop_loss_pct = 2.0
        take_profit_pct = 4.0
        
        stop_loss_value = final_value * (stop_loss_pct / 100)
        take_profit_value = final_value * (take_profit_pct / 100)
        
        print(f"  📋 PEPE TRADE PARAMETERS:")
        print(f"    Symbol: {pepe_symbol}")
        print(f"    Quantity: {final_quantity:,} contracts")
        print(f"    Entry Price: ${current_price:.8f}")
        print(f"    Position Value: ${final_value:.2f}")
        print(f"    Stop Loss (2%): ${stop_loss_value:.2f}")
        print(f"    Take Profit (4%): ${take_profit_value:.2f}")
        print(f"    Risk/Reward: 1:2")
        
        # Test actual order placement (simulation)
        print("\n6️⃣ Testing Order Placement (Simulation)...")
        
        try:
            # Simulate order placement
            order_result = order_manager.place_smart_limit_order(
                symbol=pepe_symbol,
                side='buy',
                amount=final_quantity,
                confidence=75.0  # 75% confidence
            )
            
            if order_result:
                print(f"  ✅ Order placement simulation: SUCCESS")
                print(f"  📊 Order details: BUY {final_quantity:,} {pepe_symbol}")
            else:
                print(f"  ❌ Order placement simulation: FAILED")
                return False
                
        except Exception as e:
            print(f"  ❌ Order placement error: {e}")
            return False
        
        # Success summary
        print(f"\n{'='*60}")
        print("🎉 EMERGENCY PEPE TRADING TEST: SUCCESS")
        print("="*60)
        
        print(f"\n✅ ALL TESTS PASSED:")
        print(f"  ✅ PEPE data flow working")
        print(f"  ✅ Position sizing viable")
        print(f"  ✅ Order validation passed")
        print(f"  ✅ Minimum requirements met")
        print(f"  ✅ Order placement ready")
        
        print(f"\n🚀 READY FOR LIVE PEPE TRADING:")
        print(f"  Symbol: {pepe_symbol}")
        print(f"  Position: {final_quantity:,} contracts (${final_value:.2f})")
        print(f"  Balance Required: ${final_value:.2f} of ${balance:.2f} available")
        print(f"  Safety Margin: ${balance - final_value:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Critical error in PEPE trading test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_pepe_trading()
    
    if success:
        print(f"\n🎯 EMERGENCY SOLUTION VALIDATED!")
        print(f"✅ PEPE trading is viable with current balance")
        print(f"🚀 Restart live trading with PEPE configuration")
    else:
        print(f"\n❌ EMERGENCY SOLUTION FAILED")
        print(f"🚨 Additional fixes required")
    
    sys.exit(0 if success else 1)
