#!/usr/bin/env python3
"""
🚨 CRITICAL FIXES VALIDATION (SIMPLE)
Test the key fixes without importing problematic modules
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_critical_fixes():
    """Test critical fixes without problematic imports"""
    print("🚨 CRITICAL FIXES VALIDATION (SIMPLE)")
    print("=" * 50)
    
    try:
        # Test 1: Symbol Configuration
        print("\n1️⃣ Testing Symbol Configuration...")
        
        from config.config import DEFAULT_SYMBOL
        print(f"  Default symbol: {DEFAULT_SYMBOL}")
        
        # Test 2: Liquidity Threshold Configuration
        print("\n2️⃣ Testing Liquidity Threshold Configuration...")
        
        # Read the intelligent limit order manager config directly
        config_content = ""
        try:
            with open('trading/intelligent_limit_order_manager.py', 'r') as f:
                config_content = f.read()
            
            # Check if the threshold was lowered
            if "'min_order_book_depth': 0.1" in config_content:
                print("  ✅ Liquidity threshold: Fixed to 0.1 (was 50)")
            elif "'min_order_book_depth': 50" in config_content:
                print("  ❌ Liquidity threshold: Still 50 (too high)")
            else:
                print("  ⚠️ Liquidity threshold: Configuration unclear")
                
        except Exception as e:
            print(f"  ❌ Error reading config: {e}")
        
        # Test 3: Vote Aggregation Fix
        print("\n3️⃣ Testing Vote Aggregation Fix...")
        
        try:
            from core.llm_orchestrator import LLMPromptOrchestrator, LmStudioRunner
            
            # Initialize components
            lmstudio_runner = LmStudioRunner()
            orchestrator = LLMPromptOrchestrator(lmstudio_runner)
            
            # Test SHORT decision
            mock_short_results = {
                'entry_timing': type('MockResult', (), {
                    'response': {'ACTION': 'ENTER_NOW', 'DIRECTION': 'SHORT', 'CONFIDENCE': 90},
                    'success': True
                })(),
                'opportunity_scanner': type('MockResult', (), {
                    'response': {'BEST_OPPORTUNITY': 'BEARISH_BREAKOUT', 'DECISION': 'SHORT', 'CONFIDENCE': 80},
                    'success': True
                })()
            }
            
            short_decision = orchestrator.get_aggregated_decision(mock_short_results)
            print(f"  SHORT test: {short_decision.decision.value} ({short_decision.confidence:.1f}%)")
            
            short_works = short_decision.decision.value == 'SHORT'
            
        except Exception as e:
            print(f"  ❌ Vote aggregation test failed: {e}")
            short_works = False
        
        # Test 4: Configuration File Updates
        print("\n4️⃣ Testing Configuration File Updates...")
        
        config_files_checked = 0
        doge_first_count = 0
        
        config_files = [
            'config/autonomous_trading.yaml',
            'config/config.yaml'
        ]
        
        for config_file in config_files:
            try:
                with open(config_file, 'r') as f:
                    content = f.read()
                
                # Check if DOGE is listed first in symbols
                lines = content.split('\n')
                symbols_section = False
                first_symbol = None
                
                for line in lines:
                    if 'symbols:' in line and not line.strip().startswith('#'):
                        symbols_section = True
                        continue
                    
                    if symbols_section and line.strip().startswith('- '):
                        first_symbol = line.strip()
                        break
                
                config_files_checked += 1
                if first_symbol and 'DOGE' in first_symbol:
                    doge_first_count += 1
                    print(f"  ✅ {config_file}: DOGE listed first")
                else:
                    print(f"  ❌ {config_file}: DOGE not first ({first_symbol})")
                    
            except Exception as e:
                print(f"  ⚠️ Error reading {config_file}: {e}")
        
        # Test 5: Workers Default Symbol
        print("\n5️⃣ Testing Workers Default Symbol...")
        
        try:
            with open('core/workers.py', 'r') as f:
                workers_content = f.read()
            
            if 'self.current_symbol = "DOGE/USDT:USDT"' in workers_content:
                print("  ✅ Workers default symbol: Fixed to DOGE")
                workers_fixed = True
            else:
                print("  ❌ Workers default symbol: Still BTC or other")
                workers_fixed = False
                
        except Exception as e:
            print(f"  ❌ Error reading workers.py: {e}")
            workers_fixed = False
        
        # Test 6: Timeout Configuration
        print("\n6️⃣ Testing Timeout Configuration...")
        
        timeout_files = [
            ('config/production_config.yaml', 'timeout: 30'),
            ('config/strategy.yaml', 'timeout: 30'),
            ('config/unified_llm_config.yaml', 'max_response_time: 45.0')
        ]
        
        timeout_fixes = 0
        for file_path, expected_config in timeout_files:
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                if expected_config in content:
                    print(f"  ✅ {file_path}: Timeout increased")
                    timeout_fixes += 1
                else:
                    print(f"  ❌ {file_path}: Timeout not increased")
                    
            except Exception as e:
                print(f"  ⚠️ Error reading {file_path}: {e}")
        
        # Final Assessment
        print(f"\n{'='*50}")
        print("🚨 CRITICAL FIXES VALIDATION SUMMARY")
        print("="*50)
        
        fixes_working = 0
        total_fixes = 6
        
        # Fix 1: Symbol Configuration
        if "DOGE" in DEFAULT_SYMBOL:
            print("✅ Default Symbol: DOGE configured")
            fixes_working += 1
        else:
            print("❌ Default Symbol: Not DOGE")
        
        # Fix 2: Liquidity Threshold
        if "'min_order_book_depth': 0.1" in config_content:
            print("✅ Liquidity Threshold: Fixed (0.1)")
            fixes_working += 1
        else:
            print("❌ Liquidity Threshold: Not fixed")
        
        # Fix 3: Vote Aggregation
        if short_works:
            print("✅ Vote Aggregation: SHORT trades supported")
            fixes_working += 1
        else:
            print("❌ Vote Aggregation: SHORT trades not working")
        
        # Fix 4: Config Files
        if doge_first_count >= 1:
            print("✅ Configuration Files: DOGE prioritized")
            fixes_working += 1
        else:
            print("❌ Configuration Files: DOGE not prioritized")
        
        # Fix 5: Workers
        if workers_fixed:
            print("✅ Workers Default: DOGE configured")
            fixes_working += 1
        else:
            print("❌ Workers Default: Not DOGE")
        
        # Fix 6: Timeouts
        if timeout_fixes >= 2:
            print("✅ Timeout Configuration: Increased")
            fixes_working += 1
        else:
            print("❌ Timeout Configuration: Not increased")
        
        success_rate = (fixes_working / total_fixes) * 100
        print(f"\n🎯 Fix Success Rate: {success_rate:.1f}% ({fixes_working}/{total_fixes} fixes working)")
        
        if success_rate >= 80:
            print("✅ CRITICAL FIXES SUCCESSFULLY APPLIED")
            print("🚀 System ready for DOGE scalping testing")
            print("\n📋 NEXT STEPS:")
            print("1. Test the scalping system with DOGE")
            print("2. Verify order execution works with low liquidity")
            print("3. Confirm SHORT trades can be executed")
            print("4. Monitor for LMStudio timeout errors")
        else:
            print("⚠️ Some critical fixes still need attention")
        
        print(f"\nValidation completed at: {datetime.now()}")
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Critical error in validation: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_critical_fixes()
    sys.exit(0 if success else 1)
