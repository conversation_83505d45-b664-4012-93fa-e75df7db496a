2025-07-20 16:25:40,036 - main - INFO - Epinnox v6 starting up...
2025-07-20 16:25:40,078 - core.performance_monitor - INFO - Performance monitoring started
2025-07-20 16:25:40,079 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-20 16:25:40,079 - main - INFO - Performance monitoring initialized
2025-07-20 16:25:40,100 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-20 16:25:40,102 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-20 16:25:40,106 - utils.cache_manager - INFO - Cache cleanup: removed 2 files, freed 6.83 MB
2025-07-20 16:25:40,107 - utils.cache_manager - INFO - Auto cleanup complete: 2 files removed, 6.83 MB freed
2025-07-20 16:25:40,107 - main - INFO - Cache cleanup: removed 2 files
2025-07-20 16:25:44,410 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-20 16:26:08,452 - main - INFO - Epinnox v6 starting up...
2025-07-20 16:26:08,473 - core.performance_monitor - INFO - Performance monitoring started
2025-07-20 16:26:08,473 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-20 16:26:08,473 - main - INFO - Performance monitoring initialized
2025-07-20 16:26:08,487 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-20 16:26:08,487 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-20 16:26:08,488 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-20 16:26:11,700 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-20 16:26:15,065 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-20 16:26:15,068 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-20 16:26:15,069 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-20 16:26:15,069 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-20 16:26:16,691 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-20 16:26:17,400 - data.live_data_manager - INFO - 📊 Fetching historical data for BTC/USDT:USDT to populate buffers...
2025-07-20 16:26:17,436 - data.live_data_manager - INFO - ✅ Historical 1m data loaded for BTC/USDT:USDT
2025-07-20 16:26:17,441 - data.live_data_manager - INFO - ✅ Historical 5m data loaded for BTC/USDT:USDT
2025-07-20 16:26:17,457 - data.live_data_manager - INFO - ✅ Historical 15m data loaded for BTC/USDT:USDT
2025-07-20 16:26:17,457 - data.live_data_manager - INFO - ✅ Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-20 16:26:18,196 - websocket - INFO - Websocket connected
2025-07-20 16:26:20,189 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-20 16:26:20,440 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-20 16:26:20,440 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-20 16:26:20,441 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-20 16:26:20,441 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-20 16:26:20,448 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-20 16:26:22,609 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-20 16:26:22,610 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-20 16:26:22,610 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-20 16:26:22,617 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-20 16:26:22,618 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-20 16:26:22,618 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-20 16:26:22,618 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-20 16:26:22,619 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-20 16:26:22,623 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-20 16:26:22,660 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-20 16:26:22,660 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-20 16:26:22,660 - storage.session_manager - INFO - Session Manager initialized
2025-07-20 16:26:22,666 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250720_162622_512534f5
2025-07-20 16:26:22,667 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250720_162622_512534f5
2025-07-20 16:26:22,955 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-20 16:26:22,960 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-20 16:26:22,960 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-20 16:26:22,961 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-20 16:26:22,961 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-20 16:26:22,962 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-20 16:26:22,982 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-20 16:26:22,988 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-20 16:26:22,989 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-20 16:26:22,989 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-20 16:26:22,990 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-20 16:26:22,990 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-20 16:26:22,990 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-20 16:26:26,529 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-20 16:26:26,529 - core.volatility_pause_system - INFO -   5m candles: 96/60
2025-07-20 16:26:26,530 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-20 16:26:26,530 - core.volatility_pause_system - INFO -   Recent trades: 202/100
2025-07-20 16:26:26,531 - core.volatility_pause_system - WARNING - Insufficient ATR history for baseline: 0/72
2025-07-20 16:26:26,531 - core.volatility_pause_system - INFO - ✅ Built ATR baseline: 72/72
2025-07-20 16:26:26,531 - core.volatility_pause_system - INFO - Volatility state transition: VolatilityState.NORMAL → VolatilityState.PAUSE_REQUIRED
2025-07-20 16:26:26,532 - core.volatility_pause_system - WARNING - Trading pause initiated due to extreme volatility
2025-07-20 16:26:26,532 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-20 16:26:26,533 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-20 16:26:26,533 - core.regime_detector - INFO -   ✅ 5m: 96/48 candles
2025-07-20 16:26:26,533 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-20 16:26:26,549 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-20 16:26:26,549 - core.advanced_microstructure - INFO -   Recent trades: 202/100
2025-07-20 16:26:26,549 - core.advanced_microstructure - INFO -   Orderbook history: 0/50
2025-07-20 16:26:26,550 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
2025-07-20 16:26:26,550 - core.advanced_microstructure - WARNING - ⚠️ Insufficient orderbook history (0/50), attempting to build history for BTC/USDT:USDT
2025-07-20 16:26:26,551 - core.advanced_microstructure - INFO - ✅ Built orderbook history: 50/50 snapshots
2025-07-20 16:26:58,224 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-20 16:26:58,224 - core.volatility_pause_system - INFO -   5m candles: 96/60
2025-07-20 16:26:58,224 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-20 16:26:58,224 - core.volatility_pause_system - INFO -   Recent trades: 201/100
2025-07-20 16:26:58,225 - core.volatility_pause_system - INFO - Volatility state transition: VolatilityState.PAUSE_REQUIRED → VolatilityState.NORMAL
2025-07-20 16:26:58,225 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-20 16:26:58,225 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-20 16:26:58,225 - core.regime_detector - INFO -   ✅ 5m: 96/48 candles
2025-07-20 16:26:58,225 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-20 16:26:58,226 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-20 16:26:58,226 - core.advanced_microstructure - INFO -   Recent trades: 201/100
2025-07-20 16:26:58,226 - core.advanced_microstructure - INFO -   Orderbook history: 51/50
2025-07-20 16:26:58,227 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
