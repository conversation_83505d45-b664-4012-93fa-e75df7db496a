2025-07-18 21:23:31,560 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-18 22:48:49,712 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:49:20,217 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:49:51,309 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:50:23,546 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:50:54,100 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:51:26,966 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:52:00,843 - core.advanced_microstructure - ERROR - <PERSON><PERSON>r calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:52:33,531 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:53:03,526 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:53:33,531 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:54:03,553 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:54:33,572 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:55:03,969 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:55:34,054 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:56:06,250 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:56:37,390 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:57:08,959 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:57:41,539 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:58:14,101 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:58:46,167 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:59:17,610 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 22:59:47,698 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:00:17,738 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:00:48,973 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:01:18,976 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:01:49,006 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:02:23,006 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:02:55,709 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:03:25,768 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:03:58,149 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:04:28,209 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:05:01,132 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:05:32,518 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:06:03,324 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:06:33,764 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:07:03,798 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:07:34,924 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:08:06,020 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:08:36,134 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:09:06,300 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:09:36,753 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:10:07,557 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:10:37,692 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:11:08,204 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:11:42,069 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:12:12,480 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:12:43,570 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:13:13,625 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:13:43,722 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:14:14,897 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:14:45,942 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:14:45,944 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:15:16,042 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:15:16,045 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:15:47,178 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:15:47,179 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:16:19,386 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:16:19,389 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:16:49,483 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:16:49,485 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:17:21,471 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:17:21,472 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:17:53,649 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:17:53,653 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:18:24,074 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:18:24,077 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:18:56,122 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:18:56,124 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:19:26,156 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:19:26,159 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:19:57,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:19:57,192 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:20:28,355 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:20:28,360 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:20:59,548 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:20:59,552 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:21:30,716 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:21:30,718 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:22:02,846 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:22:02,849 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:22:34,356 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:22:34,360 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:23:04,402 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:23:04,405 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:23:34,744 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:23:34,748 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:24:06,779 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:24:06,781 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:24:37,792 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:24:37,794 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:25:07,899 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:25:07,901 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:25:38,035 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:25:38,037 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:26:08,238 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:26:08,240 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:26:38,402 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:26:38,404 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:27:08,494 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:27:08,496 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:27:39,678 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:27:39,681 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:28:09,783 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:28:09,786 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:28:42,996 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:28:42,998 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:29:13,094 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:29:13,098 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:29:43,707 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:29:43,709 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:30:16,166 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:30:16,169 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:30:46,210 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:30:46,212 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:31:16,283 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:31:16,286 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:31:46,307 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:31:46,310 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:32:16,393 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:32:16,396 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:32:48,077 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:32:48,080 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:33:18,271 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:33:18,272 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:33:49,391 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:33:49,394 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:34:19,396 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:34:19,398 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:34:49,436 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:34:49,439 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:35:21,155 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:35:21,157 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:35:53,107 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:35:53,110 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:36:23,144 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:36:23,146 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:36:53,702 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:36:53,705 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:37:24,777 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:37:24,778 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:37:56,963 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:37:56,965 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:38:29,015 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:38:29,017 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:39:01,457 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:39:01,459 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:39:32,153 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:39:32,156 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:40:04,420 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:40:04,422 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:40:34,453 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:40:34,456 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:41:04,471 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:41:04,475 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:41:34,665 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:41:34,667 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:42:05,018 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:42:05,021 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:42:36,817 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:42:36,818 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:43:07,033 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:43:07,036 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:43:38,223 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:43:38,224 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:44:10,424 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:44:10,427 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:44:42,657 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:44:42,661 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:45:14,018 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:45:14,020 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:45:46,086 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:45:46,088 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:46:16,136 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:46:16,139 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:46:48,231 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:46:48,234 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:47:19,574 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:47:19,576 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:47:49,620 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:47:49,625 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:48:19,750 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:48:19,752 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:48:50,790 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:48:50,793 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:49:20,869 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:49:20,872 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:49:52,983 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:49:52,985 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:50:23,404 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:50:23,407 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:50:54,260 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:50:54,263 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:51:24,402 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:51:24,405 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:51:56,628 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:51:56,630 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:52:29,784 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:52:29,787 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:52:59,867 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:52:59,871 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:53:32,470 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:53:32,473 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:54:04,657 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:54:04,659 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:54:34,670 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:54:34,674 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:55:04,691 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:55:04,693 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:55:34,702 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:55:34,705 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:56:04,723 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:56:04,726 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:56:34,729 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:56:34,732 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:57:04,749 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:57:04,753 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:57:35,072 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:57:35,075 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:58:07,217 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:58:07,219 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:58:37,444 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:58:37,446 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:59:07,968 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:59:07,972 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-18 23:59:38,021 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-18 23:59:38,023 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:00:11,071 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:00:11,073 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:00:42,007 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:00:42,011 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:01:12,171 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:01:12,173 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:01:42,320 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:01:42,322 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:02:14,880 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:02:14,885 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:02:46,905 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:02:46,907 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:03:18,179 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:03:18,183 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:03:49,941 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:03:49,943 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:04:19,964 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:04:19,967 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:04:50,089 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:04:50,090 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:05:20,108 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:05:20,110 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:05:50,746 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:05:50,748 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:06:21,965 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:06:21,967 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:06:55,080 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:06:55,084 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:07:25,245 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:07:25,246 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:07:55,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:07:55,447 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:08:25,977 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:08:25,979 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:08:58,060 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:08:58,062 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:09:30,174 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:09:30,177 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:10:02,241 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:10:02,246 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:10:33,496 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:10:33,499 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:11:03,533 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:11:03,535 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:11:33,551 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:11:33,552 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:12:05,008 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:12:05,010 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:12:35,046 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:12:35,049 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:13:05,148 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:13:05,152 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:13:35,220 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:13:35,223 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:14:05,328 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:14:05,330 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:14:37,373 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:14:37,374 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:15:07,535 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:15:07,538 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:15:37,653 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:15:37,656 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:16:08,846 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:16:08,848 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:16:41,178 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:16:41,181 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:17:11,203 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:17:11,204 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:17:41,812 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:17:41,814 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:18:13,354 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:18:13,357 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:18:45,490 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:18:45,494 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:19:17,670 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:19:17,672 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:19:49,079 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:19:49,081 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:20:19,084 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:20:19,088 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:20:49,497 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:20:49,499 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:21:19,577 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:21:19,579 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:21:49,618 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:21:49,620 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:22:20,009 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:22:20,011 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:22:50,434 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:22:50,437 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:23:20,437 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:23:20,439 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:23:51,972 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:23:51,975 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:24:23,070 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:24:23,072 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:24:53,113 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:24:53,115 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:25:23,238 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:25:23,244 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:25:53,400 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:25:53,403 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:26:23,552 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:26:23,555 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:26:56,697 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:26:56,700 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:27:27,913 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:27:27,915 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:28:00,025 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:28:00,027 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:28:31,159 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:28:31,162 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:29:02,251 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:29:02,254 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:29:33,325 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:29:33,328 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:30:04,858 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:30:04,860 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:30:35,645 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:30:35,647 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:31:05,696 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:31:05,700 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 00:31:39,012 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:32:11,158 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:32:42,304 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:33:14,065 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:33:46,625 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:34:16,795 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:34:48,275 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:35:21,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:35:53,260 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:36:25,594 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:36:55,649 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:37:27,746 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:38:00,350 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:38:30,885 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:39:03,059 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:39:35,227 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:40:05,385 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:40:35,683 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:41:07,701 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:41:38,865 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:42:08,991 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:42:39,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:43:12,633 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:43:45,124 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:44:15,667 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:44:45,766 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:45:15,922 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:45:47,099 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:46:17,353 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:46:49,873 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:47:19,883 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:47:50,417 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:48:21,197 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:48:53,262 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:49:24,245 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:49:55,428 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:50:28,543 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:50:59,752 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:51:31,740 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:52:02,944 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:52:33,070 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:53:03,208 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:53:33,370 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:54:03,520 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:54:35,535 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:55:05,543 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:55:35,882 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:56:08,441 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:56:39,390 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:57:09,897 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:57:39,984 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:58:10,037 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:58:41,385 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:59:13,202 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 00:59:43,359 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:00:13,526 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:00:46,438 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:01:17,830 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:01:48,987 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:02:19,170 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:02:50,832 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:03:20,881 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:03:50,976 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:04:21,587 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:04:53,951 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:05:24,087 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:05:55,780 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:06:27,322 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:06:59,514 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:07:30,675 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:08:02,905 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:08:35,085 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:09:05,857 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:09:35,937 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:10:06,005 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:10:36,382 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:11:07,886 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:11:39,033 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:12:11,800 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:12:42,350 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:13:13,546 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:13:46,079 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:14:19,290 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:14:50,863 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:15:20,865 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:15:50,883 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:16:20,907 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:16:50,918 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:17:20,936 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:17:50,946 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:18:20,962 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:18:51,394 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:19:22,560 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:19:54,783 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:20:27,209 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:21:00,511 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:21:32,638 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:22:04,676 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:22:35,959 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:23:06,004 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:23:36,019 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:24:06,029 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:24:36,051 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:25:06,064 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:25:36,107 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:26:06,486 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:26:36,483 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:27:08,055 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:27:40,286 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:28:13,773 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:28:45,912 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:29:17,938 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:29:50,075 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:30:20,517 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:30:51,121 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:31:21,149 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:31:54,515 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:32:24,683 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:32:54,830 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:33:27,150 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:33:57,539 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:34:27,548 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:35:00,873 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:35:32,988 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:36:05,004 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:36:35,025 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:37:06,275 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:37:36,685 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:38:06,703 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:38:36,722 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:39:08,827 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:39:39,005 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:40:11,173 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:40:43,635 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:41:16,428 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:41:46,472 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:42:16,527 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:42:46,721 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:43:20,044 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:43:51,368 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:44:21,380 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:44:54,323 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:45:24,478 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:45:54,535 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:46:24,748 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:46:55,917 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:47:26,007 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:47:59,188 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:48:30,787 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:49:03,202 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:49:35,026 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:50:05,103 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:50:36,463 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:51:07,379 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:51:38,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:52:10,662 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:52:41,873 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:53:15,039 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:53:47,822 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:54:18,995 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:54:49,784 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:55:21,587 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:55:51,591 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:56:21,627 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:56:51,643 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:57:21,671 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:57:54,204 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:58:25,375 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:58:57,493 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:59:27,696 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 01:59:57,885 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:00:28,009 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:01:00,289 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:01:32,377 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:02:02,948 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:02:32,991 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:03:05,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:03:36,755 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:04:06,764 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:04:36,782 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:05:06,893 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:05:40,537 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:06:13,939 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:06:46,784 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:07:17,896 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:07:48,101 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:08:18,327 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:08:48,499 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:09:19,013 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:09:50,072 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:10:21,941 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:10:53,164 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:11:23,230 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:11:53,370 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:12:23,530 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:12:53,645 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:13:25,809 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:13:56,948 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:14:29,715 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:15:01,292 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:15:31,456 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:16:05,150 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:16:37,396 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:17:07,516 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:17:41,169 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:18:13,331 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:18:44,996 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:19:15,019 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:19:45,051 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:20:16,883 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:20:48,991 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:21:22,153 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:21:52,196 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:22:22,286 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:22:52,367 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:23:23,948 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:23:55,424 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:24:26,090 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:24:58,986 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:25:30,914 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:26:02,405 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:26:34,513 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:27:05,673 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:27:37,146 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:28:07,193 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:28:37,228 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:29:07,248 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:29:37,311 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:30:07,874 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:30:39,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:31:09,628 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:31:39,723 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:32:11,477 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:32:43,340 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:33:15,394 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:33:47,512 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:34:17,585 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:34:47,657 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:35:17,744 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:35:48,252 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:36:18,316 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:36:48,406 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:37:18,490 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:37:50,332 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:38:20,915 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:38:52,366 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:39:22,683 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:39:53,038 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:40:25,191 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:40:57,066 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:41:28,735 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:42:00,825 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:42:33,450 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:43:05,008 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:43:35,119 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:44:05,205 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:44:35,401 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:45:07,090 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:45:37,913 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:46:09,751 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:46:42,308 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:47:12,481 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:47:44,343 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:48:15,762 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:48:47,762 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:49:18,825 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:49:48,994 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:50:19,113 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:50:50,237 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:51:23,236 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:51:53,327 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:52:23,443 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:52:53,442 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:53:23,521 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:53:53,585 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:54:23,631 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:54:54,660 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:55:24,682 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:55:54,772 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:56:26,812 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:56:58,036 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:57:29,097 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:58:00,215 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:58:30,365 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:59:03,214 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 02:59:34,640 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:00:07,767 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:00:37,841 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:01:07,927 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:01:37,960 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:02:08,028 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:02:38,088 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:03:08,581 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:03:40,802 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:04:10,902 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:04:42,048 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:05:13,608 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:05:44,356 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:06:14,475 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:06:45,616 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:07:15,703 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:07:45,903 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:08:17,040 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:08:47,160 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:09:17,321 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:09:47,509 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:10:17,649 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:10:49,668 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:11:20,014 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:11:52,958 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:12:25,319 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:12:55,452 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:13:26,609 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:13:56,647 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:14:27,865 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:14:59,748 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:15:32,105 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:16:02,401 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:16:35,725 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:17:05,798 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:17:38,302 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:18:08,353 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:18:38,427 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:19:09,648 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:19:39,662 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:20:10,661 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:20:41,799 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:21:11,953 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:21:42,025 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:22:14,860 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:22:46,604 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:23:16,727 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:23:48,788 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:24:21,379 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:24:53,328 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:25:23,441 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:25:53,451 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:26:25,357 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:26:58,537 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:27:28,678 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:28:00,914 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:28:31,095 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:29:01,638 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:29:31,759 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:30:01,840 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:30:31,915 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:31:04,040 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:31:34,193 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:32:04,326 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:32:35,206 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:33:06,653 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:33:36,818 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:34:06,984 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:34:38,288 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:35:10,672 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:35:42,747 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:36:13,624 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:36:43,830 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:37:15,740 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:37:45,817 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:38:17,219 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:38:49,687 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:39:20,586 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:39:51,833 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:40:23,420 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:40:56,028 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:41:28,009 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:42:00,795 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:42:33,389 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:43:06,209 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:43:38,029 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:44:08,483 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:44:38,524 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:45:08,566 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:45:38,687 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:46:08,757 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:46:38,834 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:47:08,960 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:47:39,879 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:48:10,000 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:48:40,198 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:49:13,420 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:49:46,347 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:50:19,818 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:50:53,293 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:51:23,350 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:51:53,811 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:52:23,916 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:52:53,949 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:53:26,703 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:53:57,767 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:54:30,028 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:55:00,058 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:55:30,279 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:56:00,411 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:56:31,654 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:57:01,700 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:57:31,846 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:58:05,030 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:58:35,193 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:59:05,328 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 03:59:35,457 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:00:08,803 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:00:38,807 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:01:08,821 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:01:38,873 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:02:08,883 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:02:42,001 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:03:13,586 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:03:43,789 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:04:14,959 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:04:45,583 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:05:17,124 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:05:50,201 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:06:20,263 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:06:50,292 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:07:20,788 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:07:52,920 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:08:23,450 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:08:53,521 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:09:24,045 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:09:57,365 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:10:27,415 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:10:58,553 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:11:30,659 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:12:02,940 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:12:33,054 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:13:04,905 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:13:35,029 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:14:07,818 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:14:37,853 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:15:07,873 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:15:37,929 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:16:09,149 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:16:39,156 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:17:09,168 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:17:39,566 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:18:13,694 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:18:46,536 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:19:16,649 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:19:46,799 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:20:17,088 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:20:48,610 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:21:18,633 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:21:48,680 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:22:20,186 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:22:51,222 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:23:24,260 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:23:54,283 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:24:24,302 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:24:55,691 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:25:28,544 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:25:58,587 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:26:30,556 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:27:02,339 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:27:34,830 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:28:06,914 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:28:39,122 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:29:09,556 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:29:42,169 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:30:12,347 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:30:42,507 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:31:12,673 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:31:42,804 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:32:15,406 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:32:47,540 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:33:18,996 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:33:49,037 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:34:19,096 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:34:50,844 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:35:20,983 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:35:51,108 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:36:21,326 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:36:52,511 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:37:22,671 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:37:54,663 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:38:25,140 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:38:58,376 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:39:28,500 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:39:58,622 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:40:29,767 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:40:59,977 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:41:31,177 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:42:01,238 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:42:33,385 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:43:03,574 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:43:34,781 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:44:07,223 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:44:39,320 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:45:10,599 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:45:40,703 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:46:11,560 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:46:42,575 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:47:15,110 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:47:46,894 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:48:17,046 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:48:47,199 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:49:17,366 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:49:47,502 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:50:17,666 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:50:48,764 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:51:18,946 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:51:49,147 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:52:20,833 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:52:50,928 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:53:22,773 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:53:54,735 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:54:26,958 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:54:57,117 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:55:29,322 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:56:01,562 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:56:32,639 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:57:03,210 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:57:33,265 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:58:04,150 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:58:34,382 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:59:07,597 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 04:59:40,069 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:00:10,174 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:00:40,400 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:01:14,072 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:01:44,163 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:02:15,941 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:02:47,374 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:03:19,487 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:03:49,704 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:04:19,876 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:04:49,977 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:05:20,133 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:05:50,571 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:06:22,661 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:06:52,751 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:07:22,849 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:07:55,343 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:08:25,443 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:08:57,206 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:09:29,330 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:09:59,410 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:10:29,599 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:10:55,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:11:26,602 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:12:10,102 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:12:10,521 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:12:43,145 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:13:17,128 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:13:49,798 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:14:19,853 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:14:49,953 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:15:20,040 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:15:50,182 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:16:21,905 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:16:51,923 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:17:24,321 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:17:55,322 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:18:29,217 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:19:00,561 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:19:29,644 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:19:32,396 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:20:03,793 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:20:34,915 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:21:07,436 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:21:29,809 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:21:29,825 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:21:37,455 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:22:09,163 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:22:29,815 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:22:40,409 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:23:10,417 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:23:29,919 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:23:43,420 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:24:13,526 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:24:47,313 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:25:17,334 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:25:29,648 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:25:49,728 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:26:29,765 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:26:29,778 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:26:30,182 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:27:02,801 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:27:36,110 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:28:09,551 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:28:29,707 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:28:39,552 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:29:09,583 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:29:39,661 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:30:09,752 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:30:40,539 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:31:10,598 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:31:40,605 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:32:11,209 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:32:29,148 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:32:43,359 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:33:14,567 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:33:29,283 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:33:46,736 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:34:27,856 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:35:01,044 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:35:31,671 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:36:04,571 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:36:35,487 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:37:07,821 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:37:29,001 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:37:29,001 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:37:39,803 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:38:10,713 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:38:29,162 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:38:40,717 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:39:10,756 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:39:29,313 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:39:29,313 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:39:41,298 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:40:11,442 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:40:41,619 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:41:13,383 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:41:43,877 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:42:29,081 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:42:29,491 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:43:02,055 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:43:32,152 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:44:04,355 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:44:35,725 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:45:05,871 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:45:29,129 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:45:29,129 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:45:38,654 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:46:09,994 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:46:41,011 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:47:11,013 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:47:29,305 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:47:29,305 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:47:42,716 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:48:12,755 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:48:46,611 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:49:16,772 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:49:29,166 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:49:46,772 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:50:27,425 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:51:01,149 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:51:31,181 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:52:04,773 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:52:29,139 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:52:37,501 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:53:09,262 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:53:40,968 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:54:11,110 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:54:29,430 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:54:41,140 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:55:11,139 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:55:43,329 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:56:15,398 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:56:29,641 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:56:29,641 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:56:45,505 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:57:16,567 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:57:46,655 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:58:29,553 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:58:29,946 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:59:03,078 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 05:59:29,633 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:59:29,633 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:59:29,633 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 05:59:36,207 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:00:06,367 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:00:39,288 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:01:09,330 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:01:39,349 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:02:09,383 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:02:29,127 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:02:39,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:03:11,252 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:03:41,259 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:04:11,279 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:04:41,282 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:05:11,284 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:05:29,143 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:05:29,144 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:05:41,821 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:06:13,417 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:06:44,638 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:07:15,720 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:07:29,923 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:07:47,193 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:08:26,419 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:08:57,833 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:09:29,391 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:10:01,596 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:10:33,233 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:11:03,810 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:11:35,026 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:12:07,117 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:12:37,284 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:13:08,400 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:13:30,051 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:13:39,968 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:14:11,970 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:14:43,880 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:15:29,132 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:16:00,435 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:16:29,200 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:16:29,215 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:16:29,216 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:16:31,656 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:17:03,500 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:17:33,552 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:18:06,759 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:18:37,839 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:19:08,997 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:19:29,350 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:19:39,131 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:20:09,216 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:20:41,621 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:21:11,747 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:21:41,846 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:22:11,933 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:22:29,263 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:22:29,263 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:22:44,520 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:23:29,255 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:23:30,132 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:24:02,161 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:24:29,260 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:24:35,463 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:25:05,595 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:25:35,595 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:26:07,540 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:26:29,285 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:26:39,688 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:27:11,062 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:27:41,100 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:28:11,653 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:28:42,075 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:29:12,476 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:29:29,356 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:29:42,520 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:30:12,624 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:30:42,674 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:31:12,731 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:31:29,324 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:31:42,757 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:32:12,849 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:32:44,907 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:33:15,082 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:33:45,241 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:34:16,435 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:34:29,387 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:34:47,538 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:35:30,063 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:35:30,460 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:36:01,233 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:36:34,993 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:37:05,452 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:37:29,377 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:37:35,579 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:38:07,569 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:38:39,617 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:39:11,458 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:39:29,717 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:39:42,222 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:40:12,282 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:40:43,005 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:41:13,141 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:41:43,255 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:42:13,491 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:42:43,712 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:43:14,237 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:43:47,060 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:44:29,444 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:44:29,860 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:45:00,490 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:45:32,246 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:46:05,540 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:46:29,634 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:46:29,650 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:46:37,234 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:47:08,789 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:47:30,062 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:47:39,799 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:48:12,320 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:48:42,365 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:49:12,513 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:49:29,848 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:49:29,864 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:49:43,332 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:50:13,461 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:50:47,055 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:51:30,155 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:51:30,530 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:52:03,193 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:52:33,906 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:53:05,289 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:53:35,625 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:54:08,404 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:54:38,531 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:55:09,620 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:55:40,694 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:56:12,109 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:56:42,137 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:57:12,217 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:57:30,253 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:57:30,253 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 06:57:42,305 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:58:14,284 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:58:46,484 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:59:16,660 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 06:59:48,140 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:00:30,013 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:00:30,013 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:00:30,025 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:00:30,414 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:01:01,094 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:01:32,250 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:02:04,407 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:02:34,642 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:03:07,815 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:03:30,123 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:03:39,661 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:04:09,742 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:04:42,165 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:05:12,165 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:05:29,495 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:05:29,495 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:05:42,189 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:06:12,195 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:06:42,200 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:07:12,214 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:07:29,441 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:07:42,223 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:08:15,545 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:08:29,857 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:08:45,573 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:09:17,329 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:09:47,371 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:10:29,726 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:10:30,109 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:11:01,581 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:11:29,846 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:11:29,846 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:11:32,624 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:12:03,349 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:12:36,904 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:13:08,655 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:13:30,004 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:13:41,194 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:14:12,386 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:14:42,409 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:15:12,539 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:15:30,203 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:15:30,203 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:15:42,547 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:16:13,116 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:16:46,292 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:17:16,395 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:17:46,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:18:29,492 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:18:29,507 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:18:29,898 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:19:00,745 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:19:30,064 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:19:32,899 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:20:05,911 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:20:36,251 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:21:08,203 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:21:39,038 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:22:09,100 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:22:30,022 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:22:30,022 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:22:40,664 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:23:10,852 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:23:30,015 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:23:42,597 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:24:12,609 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:24:43,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:25:15,915 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:25:31,262 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:25:47,612 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:26:27,000 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:26:57,590 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:27:29,915 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:28:00,132 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:28:31,510 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:29:03,466 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:29:35,637 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:30:08,064 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:30:38,112 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:31:09,566 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:31:29,565 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:31:39,584 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:32:11,455 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:32:29,654 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:32:42,917 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:33:13,009 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:33:29,666 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:33:43,053 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:34:13,105 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:34:43,141 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:35:14,807 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:35:29,727 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:35:29,727 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:35:46,432 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:36:16,948 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:36:47,041 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:37:29,674 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:37:30,062 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:38:01,215 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:38:34,488 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:39:04,906 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:39:37,419 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:40:08,501 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:40:41,623 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:41:11,700 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:41:42,955 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:42:12,962 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:42:30,005 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:42:30,021 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:42:30,021 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:42:43,000 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:43:13,010 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:43:30,166 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:43:43,031 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:44:15,170 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:44:46,636 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:45:30,083 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:45:30,083 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:45:30,478 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:46:02,238 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:46:33,608 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:47:04,445 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:47:36,824 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:48:08,611 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
2025-07-19 07:48:39,614 - core.volatility_pause_system - ERROR - Error updating historical data: deque.pop() takes no arguments (1 given)
