"""
🗳️ Unified Vote Aggregator System
Converts all LLM responses to numeric weights and resolves conflicts
Eliminates mixed signals like REJECTED + WAIT + LONG → confusion
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class VoteType(Enum):
    LONG = "LONG"
    SHORT = "SHORT"
    WAIT = "WAIT"
    CLOSE = "CLOSE"

@dataclass
class VoteWeight:
    """Individual vote with weight and confidence"""
    vote_type: VoteType
    weight: float
    confidence: float
    source: str
    reasoning: str = ""

@dataclass
class AggregatedDecision:
    """Final aggregated decision with metadata"""
    decision: VoteType
    confidence: float
    total_weight: float
    vote_breakdown: Dict[str, float]
    consensus_strength: float
    conflicting_signals: bool
    reasoning: str

class UnifiedVoteAggregator:
    """🗳️ Unified voting system that resolves LLM conflicts intelligently"""
    
    def __init__(self):
        # 🎯 OPTIMIZED: Priority weights for crypto futures scalping
        self.prompt_weights = {
            'risk_assessment': 2.2,      # Reduced from 3.0 - less conservative for scalping
            'entry_timing': 3.0,         # Increased - timing is critical for scalping
            'opportunity_scanner': 2.5,   # Increased - opportunity identification key
            'emergency_response': 5.0,    # Maximum priority - safety first
            'market_regime': 1.8,         # Increased - crypto market context important
            'position_management': 2.3,   # Increased - active position management
            'profit_optimization': 1.5,   # Increased - profit taking important in scalping
            'strategy_adaptation': 0.8    # Increased - crypto markets need adaptation
        }
        
        # 🚨 DYNAMIC: Adaptive confidence thresholds for crypto futures scalping
        self.base_min_confidence = 55.0  # Base minimum confidence
        self.base_high_confidence = 75.0  # Base high confidence
        self.base_consensus_threshold = 0.6  # Base consensus requirement

        # Current adaptive thresholds (updated dynamically)
        self.min_confidence_threshold = self.base_min_confidence
        self.high_confidence_threshold = self.base_high_confidence
        self.consensus_threshold = self.base_consensus_threshold

        # Market condition tracking for dynamic adjustments
        self.current_market_regime = "UNKNOWN"
        self.current_volatility = 1.0
        self.current_spread_quality = 5.0

    def update_market_conditions(self, market_regime: str, volatility: float, spread_quality: float):
        """Update market conditions for dynamic weight and threshold adjustment"""
        self.current_market_regime = market_regime
        self.current_volatility = volatility
        self.current_spread_quality = spread_quality

        # Adjust weights and thresholds based on market conditions
        self._adjust_dynamic_parameters()

    def _adjust_dynamic_parameters(self):
        """Adjust weights and thresholds based on current market conditions"""
        # Reset to base values
        self.prompt_weights = {
            'risk_assessment': 2.2,
            'entry_timing': 3.0,
            'opportunity_scanner': 2.5,
            'emergency_response': 5.0,
            'market_regime': 1.8,
            'position_management': 2.3,
            'profit_optimization': 1.5,
            'strategy_adaptation': 0.8
        }

        # Adjust based on market regime
        if self.current_market_regime == "TRENDING_STRONG":
            self.prompt_weights['entry_timing'] *= 1.3  # Timing critical in trends
            self.prompt_weights['risk_assessment'] *= 0.9  # Less conservative
            self.min_confidence_threshold = self.base_min_confidence - 5  # Lower threshold
        elif self.current_market_regime == "RANGING_VOLATILE":
            self.prompt_weights['risk_assessment'] *= 1.2  # More conservative
            self.prompt_weights['opportunity_scanner'] *= 1.1  # More selective
            self.min_confidence_threshold = self.base_min_confidence + 5  # Higher threshold
        elif self.current_market_regime == "BREAKOUT_PENDING":
            self.prompt_weights['entry_timing'] *= 1.4  # Timing very critical
            self.min_confidence_threshold = self.base_min_confidence - 3  # Slightly lower

        # Adjust based on volatility
        if self.current_volatility > 2.0:  # High volatility
            self.prompt_weights['risk_assessment'] *= 1.3
            self.prompt_weights['emergency_response'] *= 1.1
            self.min_confidence_threshold += 8  # Higher threshold in volatile markets
        elif self.current_volatility < 0.5:  # Low volatility
            self.prompt_weights['opportunity_scanner'] *= 1.2  # More aggressive opportunity seeking
            self.min_confidence_threshold -= 3  # Lower threshold in calm markets

        # Adjust based on spread quality
        if self.current_spread_quality >= 8.0:  # Excellent spreads
            self.prompt_weights['entry_timing'] *= 1.2  # Take advantage of good conditions
            self.min_confidence_threshold -= 2
        elif self.current_spread_quality <= 4.0:  # Poor spreads
            self.prompt_weights['risk_assessment'] *= 1.3  # More cautious
            self.min_confidence_threshold += 5

        # Ensure thresholds stay within reasonable bounds
        self.min_confidence_threshold = max(45.0, min(80.0, self.min_confidence_threshold))
        self.high_confidence_threshold = max(self.min_confidence_threshold + 10,
                                           min(90.0, self.base_high_confidence +
                                               (self.min_confidence_threshold - self.base_min_confidence)))

    def aggregate_votes(self, llm_results: Dict[str, Any]) -> AggregatedDecision:
        """🗳️ Main aggregation method - converts LLM results to unified decision"""
        try:
            # Extract individual votes from LLM results
            votes = self._extract_votes_from_results(llm_results)
            
            if not votes:
                return self._create_default_decision("No valid votes extracted")
            
            # Calculate weighted vote totals
            vote_totals = self._calculate_weighted_totals(votes)
            
            # Apply priority rules and conflict resolution
            final_decision = self._resolve_conflicts_and_decide(vote_totals, votes)
            
            logger.info(f"🗳️ Vote aggregation: {final_decision.decision.value} ({final_decision.confidence:.1f}%) - {final_decision.reasoning}")
            
            return final_decision
            
        except Exception as e:
            logger.error(f"❌ Error in vote aggregation: {e}")
            return self._create_default_decision(f"Aggregation error: {e}")
    
    def _extract_votes_from_results(self, llm_results: Dict[str, Any]) -> List[VoteWeight]:
        """Extract and normalize votes from LLM results"""
        votes = []
        
        for prompt_type, result in llm_results.items():
            try:
                if not hasattr(result, 'response') or not result.success:
                    continue
                
                response = result.response
                prompt_name = prompt_type.value if hasattr(prompt_type, 'value') else str(prompt_type)
                
                # Extract vote based on prompt type
                vote_weight = self._extract_vote_from_response(prompt_name, response)
                if vote_weight:
                    votes.append(vote_weight)
                    
            except Exception as e:
                logger.warning(f"⚠️ Error extracting vote from {prompt_type}: {e}")
                continue
        
        return votes
    
    def _extract_vote_from_response(self, prompt_name: str, response: Dict[str, Any]) -> Optional[VoteWeight]:
        """Extract vote from individual prompt response"""
        try:
            # 🎯 RISK ASSESSMENT: APPROVED/REJECTED → WAIT/PROCEED
            if 'risk_assessment' in prompt_name.lower():
                approved = response.get('APPROVED', False)
                confidence = response.get('CONFIDENCE', 50)

                if approved:
                    # 🚨 CRITICAL FIX: Risk approved - allow directional decisions (no interference)
                    return None  # Don't interfere with directional decisions
                else:
                    # 🚨 CRITICAL FIX: Risk rejected - moderate WAIT vote (reduced from 3.0 to 2.0)
                    return VoteWeight(VoteType.WAIT, 2.0, confidence, prompt_name, "Risk rejected - moderate WAIT")
            
            # 🎯 ENTRY TIMING: ACTION → Direct vote
            elif 'entry_timing' in prompt_name.lower():
                action = response.get('ACTION', 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                
                if action in ['LONG', 'BUY', 'ENTER_NOW']:
                    # 🚨 ENHANCED: Check for direction in response
                    direction = response.get('DIRECTION', 'LONG').upper()
                    if direction == 'SHORT':
                        return VoteWeight(VoteType.SHORT, 2.5, confidence, prompt_name, "Entry timing: ENTER_NOW SHORT")
                    else:
                        return VoteWeight(VoteType.LONG, 2.5, confidence, prompt_name, "Entry timing: ENTER_NOW LONG")
                elif action in ['SHORT', 'SELL']:
                    return VoteWeight(VoteType.SHORT, 2.5, confidence, prompt_name, "Entry timing: SHORT signal")
                else:
                    return VoteWeight(VoteType.WAIT, 1.0, confidence, prompt_name, "Entry timing: WAIT signal")
            
            # 🎯 OPPORTUNITY SCANNER: BEST_OPPORTUNITY → Directional vote
            elif 'opportunity_scanner' in prompt_name.lower():
                opportunity = response.get('BEST_OPPORTUNITY', 'NONE')
                decision = response.get('DECISION', 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                
                if decision in ['LONG', 'BUY'] and opportunity != 'NONE':
                    return VoteWeight(VoteType.LONG, 2.0, confidence, prompt_name, f"Opportunity: {opportunity} → LONG")
                elif decision in ['SHORT', 'SELL'] and opportunity != 'NONE':
                    return VoteWeight(VoteType.SHORT, 2.0, confidence, prompt_name, f"Opportunity: {opportunity} → SHORT")
                else:
                    return VoteWeight(VoteType.WAIT, 0.5, confidence, prompt_name, "No clear opportunity")
            
            # 🎯 EMERGENCY RESPONSE: Highest priority override
            elif 'emergency' in prompt_name.lower():
                action = response.get('ACTION', 'MONITOR').upper()
                confidence = response.get('CONFIDENCE', 100)
                
                if action == 'CLOSE_ALL':
                    return VoteWeight(VoteType.CLOSE, 5.0, confidence, prompt_name, "Emergency: CLOSE ALL")
                elif action != 'MONITOR':
                    return VoteWeight(VoteType.WAIT, 5.0, confidence, prompt_name, f"Emergency: {action}")
                
            # 🎯 GENERIC: Try to extract any ACTION/DECISION
            else:
                action = (response.get('ACTION') or response.get('DECISION') or 'WAIT').upper()
                confidence = response.get('CONFIDENCE', 50)
                weight = self.prompt_weights.get(prompt_name.lower(), 1.0)
                
                if action in ['LONG', 'BUY']:
                    return VoteWeight(VoteType.LONG, weight, confidence, prompt_name, f"Generic: {action}")
                elif action in ['SHORT', 'SELL']:
                    return VoteWeight(VoteType.SHORT, weight, confidence, prompt_name, f"Generic: {action}")
                elif action in ['CLOSE', 'EXIT']:
                    return VoteWeight(VoteType.CLOSE, weight, confidence, prompt_name, f"Generic: {action}")
                else:
                    return VoteWeight(VoteType.WAIT, weight * 0.5, confidence, prompt_name, f"Generic: {action}")
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ Error extracting vote from {prompt_name}: {e}")
            return None
    
    def _calculate_weighted_totals(self, votes: List[VoteWeight]) -> Dict[VoteType, float]:
        """Calculate weighted vote totals"""
        totals = {vote_type: 0.0 for vote_type in VoteType}
        
        for vote in votes:
            # Apply confidence weighting (low confidence votes get reduced weight)
            confidence_multiplier = max(0.1, vote.confidence / 100.0)
            weighted_vote = vote.weight * confidence_multiplier
            totals[vote.vote_type] += weighted_vote
            
        return totals
    
    def _resolve_conflicts_and_decide(self, vote_totals: Dict[VoteType, float], votes: List[VoteWeight]) -> AggregatedDecision:
        """Apply conflict resolution rules and make final decision"""
        # Find the winning vote
        max_vote_type = max(vote_totals, key=vote_totals.get)
        max_weight = vote_totals[max_vote_type]
        total_weight = sum(vote_totals.values())

        # Calculate consensus strength
        consensus_strength = max_weight / total_weight if total_weight > 0 else 0

        # Check for conflicting signals
        conflicting_signals = self._detect_conflicts(vote_totals)

        # Adjust WAIT threshold for scalping optimization
        if conflicting_signals and consensus_strength < 0.3:
            decision = VoteType.WAIT
            confidence = 50.0
        elif max_vote_type != VoteType.WAIT and max_weight >= 0.6:
            decision = max_vote_type
            confidence = max_weight * 100 / total_weight
        else:
            decision = VoteType.WAIT
            confidence = 50.0

        reasoning = f"Decision based on weighted votes: {decision.value} ({confidence:.1f}%)"

        return AggregatedDecision(
            decision=decision,
            confidence=confidence,
            total_weight=total_weight,
            vote_breakdown={vt.value: wt for vt, wt in vote_totals.items()},
            consensus_strength=consensus_strength,
            conflicting_signals=conflicting_signals,
            reasoning=reasoning
        )
    
    def _detect_conflicts(self, vote_totals: Dict[VoteType, float]) -> bool:
        """Detect conflicting signals (e.g., LONG vs SHORT both high)"""
        long_weight = vote_totals[VoteType.LONG]
        short_weight = vote_totals[VoteType.SHORT]
        
        # Conflict if both LONG and SHORT have significant weight
        if long_weight > 1.0 and short_weight > 1.0:
            return True
            
        # Conflict if any two non-WAIT votes are close
        non_wait_votes = [(vt, weight) for vt, weight in vote_totals.items() if vt != VoteType.WAIT and weight > 0.5]
        if len(non_wait_votes) >= 2:
            sorted_votes = sorted(non_wait_votes, key=lambda x: x[1], reverse=True)
            if len(sorted_votes) >= 2 and abs(sorted_votes[0][1] - sorted_votes[1][1]) < 0.5:
                return True
        
        return False
    
    def _create_default_decision(self, reason: str) -> AggregatedDecision:
        """Create default WAIT decision"""
        return AggregatedDecision(
            decision=VoteType.WAIT,
            confidence=50.0,
            total_weight=0.0,
            vote_breakdown={vt.value: 0.0 for vt in VoteType},
            consensus_strength=0.0,
            conflicting_signals=False,
            reasoning=reason
        )
