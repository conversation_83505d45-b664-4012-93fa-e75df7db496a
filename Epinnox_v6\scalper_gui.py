#!/usr/bin/env python3
"""
ScalperGPT Dynamic GUI Interface
Auto-generating PyQt5 interface for Epinnox v6 Trading System

This module provides a comprehensive GUI that automatically discovers and exposes
all configurable parameters from the trading system, allowing real-time parameter
updates and manual trading overrides.
"""

import sys
import os
import inspect
import json
import yaml
from typing import Dict, Any, List, Optional, Union, Type
from dataclasses import dataclass, fields, is_dataclass
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 imports
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    import pyqtgraph as pg
    pg.setConfigOptions(useOpenGL=False, antialias=True)
except ImportError as e:
    print(f"❌ PyQt5 not available: {e}")
    print("Install with: pip install PyQt5 pyqtgraph")
    sys.exit(1)

# Import Matrix theme
try:
    from gui.matrix_theme import MatrixTheme
except ImportError:
    # Fallback Matrix theme if not available
    class MatrixTheme:
        BLACK = "#000000"
        GREEN = "#00FF00"
        DARK_GREEN = "#003300"
        MID_GREEN = "#006600"
        LIGHT_GREEN = "#00CC00"
        RED = "#FF0000"
        YELLOW = "#FFFF00"
        WHITE = "#FFFFFF"
        GRAY = "#666666"
        DARK_GRAY = "#333333"
        BRIGHT_GREEN = "#00FF88"
        CYAN = "#00FFFF"
        BLUE = "#0088FF"
        DARK_BLUE = "#003366"
        PURPLE = "#8800FF"
        DARK_PURPLE = "#330066"
        FONT_FAMILY = "Courier New"
        FONT_SIZE = 12
        
        @classmethod
        def get_stylesheet(cls):
            return f"""
            * {{ background-color: {cls.BLACK}; color: {cls.GREEN}; }}
            QMainWindow {{ background-color: {cls.BLACK}; color: {cls.GREEN}; font-family: {cls.FONT_FAMILY}; }}
            QWidget {{ background-color: {cls.BLACK}; color: {cls.GREEN}; }}
            QTabWidget {{ background-color: {cls.BLACK}; }}
            QTabWidget::pane {{ border: 1px solid {cls.DARK_GREEN}; background-color: {cls.BLACK}; }}
            QTabBar::tab {{ background-color: {cls.DARK_GREEN}; color: {cls.GREEN}; padding: 8px 16px; margin: 2px; border: 1px solid {cls.GREEN}; }}
            QTabBar::tab:selected {{ background-color: {cls.GREEN}; color: {cls.BLACK}; font-weight: bold; }}
            QGroupBox {{ border: 2px solid {cls.DARK_GREEN}; border-radius: 5px; margin: 5px; padding-top: 10px; color: {cls.GREEN}; font-weight: bold; background-color: {cls.BLACK}; }}
            QGroupBox::title {{ subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; color: {cls.LIGHT_GREEN}; }}
            QPushButton {{ background-color: {cls.DARK_GREEN}; color: {cls.GREEN}; border: 2px solid {cls.GREEN}; padding: 8px 16px; font-weight: bold; border-radius: 3px; }}
            QPushButton:hover {{ background-color: {cls.GREEN}; color: {cls.BLACK}; }}
            QPushButton:pressed {{ background-color: {cls.LIGHT_GREEN}; color: {cls.BLACK}; }}
            QLabel {{ color: {cls.GREEN}; background-color: transparent; }}
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{ background-color: {cls.BLACK}; color: {cls.GREEN}; border: 1px solid {cls.DARK_GREEN}; padding: 5px; border-radius: 3px; }}
            QComboBox::drop-down {{ background-color: {cls.DARK_GREEN}; border: 1px solid {cls.GREEN}; }}
            QComboBox::down-arrow {{ border: none; }}
            QComboBox QAbstractItemView {{ background-color: {cls.BLACK}; color: {cls.GREEN}; selection-background-color: {cls.DARK_GREEN}; }}
            QTextEdit {{ background-color: {cls.BLACK}; color: {cls.GREEN}; border: 1px solid {cls.DARK_GREEN}; font-family: {cls.FONT_FAMILY}; }}
            QTableWidget {{ background-color: {cls.BLACK}; color: {cls.GREEN}; gridline-color: {cls.DARK_GREEN}; border: 1px solid {cls.DARK_GREEN}; }}
            QTableWidget::item {{ background-color: {cls.BLACK}; color: {cls.GREEN}; border-bottom: 1px solid {cls.DARK_GREEN}; }}
            QTableWidget::item:selected {{ background-color: {cls.DARK_GREEN}; color: {cls.LIGHT_GREEN}; }}
            QHeaderView::section {{ background-color: {cls.DARK_GREEN}; color: {cls.GREEN}; padding: 5px; border: 1px solid {cls.GREEN}; font-weight: bold; }}
            QCheckBox {{ color: {cls.GREEN}; spacing: 5px; background-color: transparent; }}
            QCheckBox::indicator {{ width: 18px; height: 18px; }}
            QCheckBox::indicator:unchecked {{ border: 2px solid {cls.GREEN}; background-color: {cls.BLACK}; }}
            QCheckBox::indicator:checked {{ border: 2px solid {cls.GREEN}; background-color: {cls.GREEN}; }}
            QSlider {{ background-color: transparent; }}
            QSlider::groove:horizontal {{ border: 1px solid {cls.DARK_GREEN}; height: 8px; background: {cls.BLACK}; margin: 2px 0; }}
            QSlider::handle:horizontal {{ background: {cls.GREEN}; border: 1px solid {cls.DARK_GREEN}; width: 18px; margin: -2px 0; border-radius: 3px; }}
            QListWidget {{ background-color: {cls.BLACK}; color: {cls.GREEN}; border: 1px solid {cls.DARK_GREEN}; }}
            QListWidget::item {{ background-color: {cls.BLACK}; color: {cls.GREEN}; padding: 3px; }}
            QListWidget::item:selected {{ background-color: {cls.DARK_GREEN}; color: {cls.LIGHT_GREEN}; }}
            QSplitter {{ background-color: {cls.BLACK}; }}
            QSplitter::handle {{ background-color: {cls.DARK_GREEN}; }}
            """

@dataclass
class ParameterInfo:
    """Information about a discoverable parameter"""
    name: str
    param_type: Type
    default_value: Any
    description: str
    category: str
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    choices: Optional[List[str]] = None
    unit: Optional[str] = None
    tooltip: Optional[str] = None
    validation_func: Optional[callable] = None

class ParameterDiscovery:
    """Discovers all configurable parameters from the codebase"""
    
    def __init__(self):
        self.discovered_parameters: Dict[str, ParameterInfo] = {}
        self.config_classes = []
        self.config_files = []
        
    def discover_all_parameters(self) -> Dict[str, ParameterInfo]:
        """Discover all parameters from various sources"""
        print("🔍 Starting parameter discovery...")
        
        # Discover from dataclasses
        self._discover_from_dataclasses()
        
        # Discover from YAML config files
        self._discover_from_yaml_configs()
        
        # Discover from Python config modules
        self._discover_from_python_configs()
        
        print(f"✅ Discovered {len(self.discovered_parameters)} parameters")
        return self.discovered_parameters
    
    def _discover_from_dataclasses(self):
        """Discover parameters from dataclass configurations"""
        try:
            # Import configuration dataclasses
            from config.autonomous_config import (
                TradingConfig, RiskConfig, MLConfig, RLConfig, 
                SymbolScannerConfig, MonitoringConfig
            )
            from config.trading_config import TradingConfig as MainTradingConfig
            
            dataclass_configs = [
                (TradingConfig, "Trading"),
                (RiskConfig, "Risk Management"),
                (MLConfig, "Machine Learning"),
                (RLConfig, "Reinforcement Learning"),
                (SymbolScannerConfig, "Symbol Scanner"),
                (MonitoringConfig, "Monitoring"),
                (MainTradingConfig, "Main Trading")
            ]
            
            for config_class, category in dataclass_configs:
                if is_dataclass(config_class):
                    self._extract_dataclass_fields(config_class, category)
                    
        except ImportError as e:
            print(f"⚠️ Could not import some config classes: {e}")
    
    def _extract_dataclass_fields(self, config_class: Type, category: str):
        """Extract fields from a dataclass"""
        try:
            for field in fields(config_class):
                param_info = self._create_parameter_info(field, category)
                if param_info:
                    key = f"{category.lower().replace(' ', '_')}_{field.name}"
                    self.discovered_parameters[key] = param_info
        except Exception as e:
            print(f"⚠️ Error extracting fields from {config_class.__name__}: {e}")
    
    def _create_parameter_info(self, field, category: str) -> Optional[ParameterInfo]:
        """Create ParameterInfo from a dataclass field"""
        try:
            param_type = field.type
            default_value = field.default if field.default is not field.default_factory else None
            
            # Handle typing annotations
            if hasattr(param_type, '__origin__'):
                if param_type.__origin__ is list:
                    param_type = list
                elif param_type.__origin__ is dict:
                    param_type = dict
                elif param_type.__origin__ is Union:
                    # Get the first non-None type
                    args = [arg for arg in param_type.__args__ if arg is not type(None)]
                    param_type = args[0] if args else str
            
            # Determine constraints based on field name and type
            min_value, max_value, choices, unit = self._infer_constraints(field.name, param_type, default_value)
            
            return ParameterInfo(
                name=field.name,
                param_type=param_type,
                default_value=default_value,
                description=self._generate_description(field.name),
                category=category,
                min_value=min_value,
                max_value=max_value,
                choices=choices,
                unit=unit,
                tooltip=self._generate_tooltip(field.name, param_type)
            )
        except Exception as e:
            print(f"⚠️ Error creating parameter info for {field.name}: {e}")
            return None
    
    def _infer_constraints(self, field_name: str, param_type: Type, default_value: Any):
        """Infer parameter constraints based on name and type with optimal defaults"""
        min_value = max_value = choices = unit = None

        # Percentage fields with optimal scalping defaults
        if 'pct' in field_name or 'percent' in field_name or 'ratio' in field_name:
            min_value, max_value, unit = 0.0, 1.0, "%"
            # Set optimal defaults for scalping
            if 'stop_loss' in field_name:
                default_value = 0.015  # 1.5% stop loss
            elif 'take_profit' in field_name:
                default_value = 0.025  # 2.5% take profit
            elif 'risk' in field_name:
                default_value = 0.02   # 2% risk per trade

        # Risk and confidence fields with optimal thresholds
        elif 'confidence' in field_name or 'threshold' in field_name:
            min_value, max_value = 0.0, 1.0
            if 'confidence' in field_name:
                default_value = 0.75   # 75% confidence threshold
            elif 'quality' in field_name:
                default_value = 0.70   # 70% quality threshold

        # Leverage fields with optimal scalping leverage
        elif 'leverage' in field_name:
            min_value, max_value, unit = 1.0, 100.0, "x"
            default_value = 20.0       # 20x leverage for scalping

        # Temperature fields (LLM) - optimal for decisive trading
        elif 'temperature' in field_name:
            min_value, max_value = 0.0, 2.0
            default_value = 0.3        # Low temperature for consistent decisions

        # Token fields (LLM) - optimal for fast responses
        elif 'token' in field_name:
            min_value, max_value = 1, 4096
            default_value = 768        # Optimal token count for speed vs detail

        # Interval/delay fields with optimal scalping timing
        elif 'interval' in field_name or 'delay' in field_name:
            min_value, max_value, unit = 0.1, 3600.0, "sec"
            if 'analysis' in field_name:
                default_value = 5.0    # 5 second analysis interval
            elif 'decision' in field_name:
                default_value = 30.0   # 30 second decision interval
            else:
                default_value = 1.0    # 1 second default

        # Position size with optimal scalping defaults
        elif 'position' in field_name and 'size' in field_name:
            min_value, max_value, unit = 0.001, 1.0, "ratio"
            default_value = 0.3        # 30% of account per position

        # Max positions for scalping
        elif 'max_positions' in field_name:
            min_value, max_value = 1, 10
            default_value = 3          # Max 3 concurrent positions

        # Boolean choices with optimal defaults
        elif param_type is bool:
            choices = ["True", "False"]
            if 'autonomous' in field_name:
                default_value = True   # Enable autonomous mode
            elif 'use_rl' in field_name:
                default_value = True   # Enable RL
            elif 'paper_trading' in field_name:
                default_value = False  # Live trading by default

        # Mode/strategy choices with scalping focus
        elif 'mode' in field_name:
            choices = ["conservative", "moderate", "aggressive", "scalping", "swing"]
            default_value = "scalping" # Default to scalping mode

        # Symbol choices with optimal scalping pairs
        elif 'symbol' in field_name and param_type is str:
            choices = ["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "ADA/USDT:USDT"]
            default_value = "DOGE/USDT:USDT"  # Default to DOGE (lower fees)

        # Timeframe choices optimized for scalping
        elif 'timeframe' in field_name:
            choices = ["1m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"]
            default_value = "1m"       # 1-minute for scalping

        # Daily trade limits for scalping
        elif 'daily' in field_name and 'trade' in field_name:
            min_value, max_value = 1, 100
            default_value = 20         # 20 trades per day max

        return min_value, max_value, choices, unit
    
    def _generate_description(self, field_name: str) -> str:
        """Generate human-readable description from field name"""
        # Convert snake_case to Title Case
        words = field_name.replace('_', ' ').split()
        return ' '.join(word.capitalize() for word in words)
    
    def _generate_tooltip(self, field_name: str, param_type: Type) -> str:
        """Generate helpful tooltip for the parameter"""
        tooltips = {
            'initial_balance': 'Starting balance for trading account',
            'max_positions': 'Maximum number of concurrent positions',
            'min_confidence': 'Minimum confidence threshold for trade execution',
            'max_leverage': 'Maximum leverage allowed for positions',
            'stop_loss_pct': 'Stop loss percentage from entry price',
            'take_profit_pct': 'Take profit percentage from entry price',
            'temperature': 'LLM temperature (0=deterministic, 1=creative)',
            'max_tokens': 'Maximum tokens in LLM response',
            'symbols': 'List of trading symbols to monitor',
            'timeframe': 'Chart timeframe for analysis',
            'analysis_interval': 'Seconds between analysis cycles',
            'quality_threshold': 'Minimum quality score for signals'
        }
        
        return tooltips.get(field_name, f"{param_type.__name__} parameter")
    
    def _discover_from_yaml_configs(self):
        """Discover parameters from YAML configuration files"""
        config_files = [
            'config/config.yaml',
            'config/trading_config.yaml',
            'config/models_config.yaml',
            'config/unified_llm_config.yaml',
            'config/strategy.yaml',
            'config/production_config.yaml'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r') as f:
                        config_data = yaml.safe_load(f)
                    self._extract_yaml_parameters(config_data, os.path.basename(config_file))
                except Exception as e:
                    print(f"⚠️ Error loading {config_file}: {e}")
    
    def _extract_yaml_parameters(self, config_data: Dict, source_file: str):
        """Extract parameters from YAML configuration data"""
        def extract_recursive(data: Dict, prefix: str = "", category: str = "Configuration"):
            for key, value in data.items():
                full_key = f"{prefix}_{key}" if prefix else key
                
                if isinstance(value, dict):
                    # Recursive extraction for nested dictionaries
                    extract_recursive(value, full_key, category)
                else:
                    # Create parameter info for leaf values
                    param_type = type(value)
                    min_val, max_val, choices, unit = self._infer_constraints(key, param_type, value)
                    
                    param_info = ParameterInfo(
                        name=key,
                        param_type=param_type,
                        default_value=value,
                        description=self._generate_description(key),
                        category=f"{category} ({source_file})",
                        min_value=min_val,
                        max_value=max_val,
                        choices=choices,
                        unit=unit,
                        tooltip=self._generate_tooltip(key, param_type)
                    )
                    
                    self.discovered_parameters[f"{source_file}_{full_key}"] = param_info
        
        extract_recursive(config_data)
    
    def _discover_from_python_configs(self):
        """Discover parameters from Python configuration modules"""
        try:
            # Import and extract from config modules
            import config.config as main_config
            
            # Extract module-level constants
            for name in dir(main_config):
                if not name.startswith('_') and name.isupper():
                    value = getattr(main_config, name)
                    if isinstance(value, (int, float, str, bool)):
                        param_type = type(value)
                        min_val, max_val, choices, unit = self._infer_constraints(name.lower(), param_type, value)
                        
                        param_info = ParameterInfo(
                            name=name.lower(),
                            param_type=param_type,
                            default_value=value,
                            description=self._generate_description(name.lower()),
                            category="System Configuration",
                            min_value=min_val,
                            max_value=max_val,
                            choices=choices,
                            unit=unit,
                            tooltip=self._generate_tooltip(name.lower(), param_type)
                        )
                        
                        self.discovered_parameters[f"system_{name.lower()}"] = param_info
                        
        except ImportError as e:
            print(f"⚠️ Could not import config modules: {e}")

class WidgetFactory:
    """Factory for creating PyQt5 widgets based on parameter types"""

    @staticmethod
    def create_widget(param_info: ParameterInfo) -> QWidget:
        """Create appropriate widget for parameter"""
        param_type = param_info.param_type

        # Boolean parameters -> Checkbox
        if param_type is bool or param_info.choices == ["True", "False"]:
            return WidgetFactory._create_checkbox(param_info)

        # Choice parameters -> ComboBox
        elif param_info.choices:
            return WidgetFactory._create_combobox(param_info)

        # Numeric parameters with range -> Slider + SpinBox
        elif param_type in (int, float) and param_info.min_value is not None and param_info.max_value is not None:
            return WidgetFactory._create_slider_spinbox(param_info)

        # Integer parameters -> SpinBox
        elif param_type is int:
            return WidgetFactory._create_spinbox(param_info)

        # Float parameters -> DoubleSpinBox
        elif param_type is float:
            return WidgetFactory._create_double_spinbox(param_info)

        # List parameters -> List widget
        elif param_type is list:
            return WidgetFactory._create_list_widget(param_info)

        # String parameters -> LineEdit
        else:
            return WidgetFactory._create_line_edit(param_info)

    @staticmethod
    def _create_checkbox(param_info: ParameterInfo) -> QCheckBox:
        """Create checkbox widget"""
        checkbox = QCheckBox(param_info.description)
        checkbox.setChecked(bool(param_info.default_value))
        checkbox.setToolTip(param_info.tooltip)
        return checkbox

    @staticmethod
    def _create_combobox(param_info: ParameterInfo) -> QComboBox:
        """Create combobox widget"""
        combobox = QComboBox()
        combobox.addItems(param_info.choices)

        # Set current value
        if param_info.default_value in param_info.choices:
            combobox.setCurrentText(str(param_info.default_value))

        combobox.setToolTip(param_info.tooltip)
        return combobox

    @staticmethod
    def _create_slider_spinbox(param_info: ParameterInfo) -> QWidget:
        """Create slider with spinbox widget"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create slider
        slider = QSlider(Qt.Horizontal)

        # Create spinbox
        if param_info.param_type is int:
            spinbox = QSpinBox()
            slider.setMinimum(int(param_info.min_value))
            slider.setMaximum(int(param_info.max_value))
            slider.setValue(int(param_info.default_value or 0))
            spinbox.setMinimum(int(param_info.min_value))
            spinbox.setMaximum(int(param_info.max_value))
            spinbox.setValue(int(param_info.default_value or 0))
        else:
            spinbox = QDoubleSpinBox()
            # Scale slider for float values
            scale = 100
            slider.setMinimum(int(param_info.min_value * scale))
            slider.setMaximum(int(param_info.max_value * scale))
            slider.setValue(int((param_info.default_value or 0) * scale))
            spinbox.setMinimum(param_info.min_value)
            spinbox.setMaximum(param_info.max_value)
            spinbox.setValue(param_info.default_value or 0)
            spinbox.setDecimals(3)
            spinbox.setSingleStep(0.001)

        # Connect slider and spinbox
        if param_info.param_type is int:
            slider.valueChanged.connect(spinbox.setValue)
            spinbox.valueChanged.connect(slider.setValue)
        else:
            slider.valueChanged.connect(lambda v: spinbox.setValue(v / 100))
            spinbox.valueChanged.connect(lambda v: slider.setValue(int(v * 100)))

        # Add unit label if available
        if param_info.unit:
            unit_label = QLabel(param_info.unit)
            layout.addWidget(unit_label)

        layout.addWidget(slider)
        layout.addWidget(spinbox)

        container.setToolTip(param_info.tooltip)
        return container

    @staticmethod
    def _create_spinbox(param_info: ParameterInfo) -> QSpinBox:
        """Create integer spinbox widget"""
        spinbox = QSpinBox()

        if param_info.min_value is not None:
            spinbox.setMinimum(int(param_info.min_value))
        else:
            spinbox.setMinimum(-999999)

        if param_info.max_value is not None:
            spinbox.setMaximum(int(param_info.max_value))
        else:
            spinbox.setMaximum(999999)

        spinbox.setValue(int(param_info.default_value or 0))
        spinbox.setToolTip(param_info.tooltip)

        if param_info.unit:
            spinbox.setSuffix(f" {param_info.unit}")

        return spinbox

    @staticmethod
    def _create_double_spinbox(param_info: ParameterInfo) -> QDoubleSpinBox:
        """Create float spinbox widget"""
        spinbox = QDoubleSpinBox()

        if param_info.min_value is not None:
            spinbox.setMinimum(param_info.min_value)
        else:
            spinbox.setMinimum(-999999.0)

        if param_info.max_value is not None:
            spinbox.setMaximum(param_info.max_value)
        else:
            spinbox.setMaximum(999999.0)

        spinbox.setValue(param_info.default_value or 0.0)
        spinbox.setDecimals(4)
        spinbox.setSingleStep(0.0001)
        spinbox.setToolTip(param_info.tooltip)

        if param_info.unit:
            spinbox.setSuffix(f" {param_info.unit}")

        return spinbox

    @staticmethod
    def _create_line_edit(param_info: ParameterInfo) -> QLineEdit:
        """Create line edit widget"""
        line_edit = QLineEdit()
        line_edit.setText(str(param_info.default_value or ""))
        line_edit.setToolTip(param_info.tooltip)
        return line_edit

    @staticmethod
    def _create_list_widget(param_info: ParameterInfo) -> QWidget:
        """Create list widget with add/remove functionality"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)

        # List widget
        list_widget = QListWidget()
        list_widget.setMaximumHeight(100)

        # Add default items
        if param_info.default_value and isinstance(param_info.default_value, list):
            for item in param_info.default_value:
                list_widget.addItem(str(item))

        # Control buttons
        button_layout = QHBoxLayout()
        add_button = QPushButton("Add")
        remove_button = QPushButton("Remove")

        def add_item():
            text, ok = QInputDialog.getText(container, "Add Item", "Enter value:")
            if ok and text:
                list_widget.addItem(text)

        def remove_item():
            current_row = list_widget.currentRow()
            if current_row >= 0:
                list_widget.takeItem(current_row)

        add_button.clicked.connect(add_item)
        remove_button.clicked.connect(remove_item)

        button_layout.addWidget(add_button)
        button_layout.addWidget(remove_button)

        layout.addWidget(list_widget)
        layout.addLayout(button_layout)

        container.setToolTip(param_info.tooltip)
        return container

class BaseConfigPanel(QWidget):
    """Base class for configuration panels with consistent styling"""

    # Signal emitted when parameter changes
    parameter_changed = pyqtSignal(str, object)  # parameter_name, new_value

    def __init__(self, title: str, parent=None):
        super().__init__(parent)
        self.title = title
        self.parameters = {}
        self.widgets = {}
        self.setup_ui()

    def setup_ui(self):
        """Setup the panel UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # Title label
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.LIGHT_GREEN};
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
                border-bottom: 2px solid {MatrixTheme.DARK_GREEN};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)

        # Scroll area for parameters
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(8)

        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)

        # Apply panel styling
        self.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {MatrixTheme.DARK_GREEN};
                background-color: {MatrixTheme.BLACK};
            }}
            QScrollArea QWidget {{
                background-color: {MatrixTheme.BLACK};
            }}
            QWidget {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
            }}
            QScrollBar:vertical {{
                background-color: {MatrixTheme.DARK_GREEN};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {MatrixTheme.GREEN};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {MatrixTheme.LIGHT_GREEN};
            }}
        """)

    def add_parameter(self, param_id: str, param_info: ParameterInfo):
        """Add a parameter to the panel"""
        self.parameters[param_id] = param_info

        # Create parameter group
        group_widget = self._create_parameter_group(param_id, param_info)
        self.content_layout.addWidget(group_widget)

    def _create_parameter_group(self, param_id: str, param_info: ParameterInfo) -> QWidget:
        """Create a parameter group with label and widget"""
        group_widget = QWidget()
        group_layout = QHBoxLayout(group_widget)
        group_layout.setContentsMargins(5, 5, 5, 5)

        # Parameter label
        label = QLabel(param_info.description)
        label.setMinimumWidth(200)
        label.setWordWrap(True)
        label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Parameter widget
        widget = WidgetFactory.create_widget(param_info)
        self.widgets[param_id] = widget

        # Connect change signals
        self._connect_widget_signals(param_id, widget, param_info)

        group_layout.addWidget(label)
        group_layout.addWidget(widget)
        group_layout.addStretch()

        return group_widget

    def _connect_widget_signals(self, param_id: str, widget: QWidget, param_info: ParameterInfo):
        """Connect widget signals to parameter change handler"""
        if isinstance(widget, QCheckBox):
            widget.toggled.connect(lambda checked: self.parameter_changed.emit(param_id, checked))
        elif isinstance(widget, QComboBox):
            widget.currentTextChanged.connect(lambda text: self.parameter_changed.emit(param_id, text))
        elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
            widget.valueChanged.connect(lambda value: self.parameter_changed.emit(param_id, value))
        elif isinstance(widget, QLineEdit):
            widget.textChanged.connect(lambda text: self.parameter_changed.emit(param_id, text))
        elif hasattr(widget, 'layout'):  # Complex widgets like slider+spinbox
            # Find the actual input widget
            for i in range(widget.layout().count()):
                child = widget.layout().itemAt(i).widget()
                if isinstance(child, (QSpinBox, QDoubleSpinBox)):
                    child.valueChanged.connect(lambda value: self.parameter_changed.emit(param_id, value))
                    break

    def get_parameter_value(self, param_id: str):
        """Get current value of a parameter"""
        if param_id not in self.widgets:
            return None

        widget = self.widgets[param_id]

        if isinstance(widget, QCheckBox):
            return widget.isChecked()
        elif isinstance(widget, QComboBox):
            return widget.currentText()
        elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
            return widget.value()
        elif isinstance(widget, QLineEdit):
            return widget.text()
        elif hasattr(widget, 'layout'):  # Complex widgets
            for i in range(widget.layout().count()):
                child = widget.layout().itemAt(i).widget()
                if isinstance(child, (QSpinBox, QDoubleSpinBox)):
                    return child.value()

        return None

    def set_parameter_value(self, param_id: str, value):
        """Set value of a parameter"""
        if param_id not in self.widgets:
            return

        widget = self.widgets[param_id]

        try:
            if isinstance(widget, QCheckBox):
                widget.setChecked(bool(value))
            elif isinstance(widget, QComboBox):
                widget.setCurrentText(str(value))
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.setValue(value)
            elif isinstance(widget, QLineEdit):
                widget.setText(str(value))
            elif hasattr(widget, 'layout'):  # Complex widgets
                for i in range(widget.layout().count()):
                    child = widget.layout().itemAt(i).widget()
                    if isinstance(child, (QSpinBox, QDoubleSpinBox)):
                        child.setValue(value)
                        break
        except Exception as e:
            print(f"⚠️ Error setting parameter {param_id} to {value}: {e}")

class SymbolsDataPanel(BaseConfigPanel):
    """Panel for symbols and data configuration"""

    def __init__(self, parent=None):
        super().__init__("📊 Symbols & Data Configuration", parent)

class LLMPromptsPanel(BaseConfigPanel):
    """Panel for LLM prompts configuration"""

    def __init__(self, parent=None):
        super().__init__("🤖 LLM Prompts Configuration", parent)

class ExecutionRulesPanel(BaseConfigPanel):
    """Panel for execution rules configuration"""

    def __init__(self, parent=None):
        super().__init__("⚡ Execution Rules Configuration", parent)

class RiskManagementPanel(BaseConfigPanel):
    """Panel for risk management configuration"""

    def __init__(self, parent=None):
        super().__init__("🛡️ Risk Management Configuration", parent)

class MonitoringLoggingPanel(BaseConfigPanel):
    """Panel for monitoring and logging configuration"""

    def __init__(self, parent=None):
        super().__init__("📈 Monitoring & Logging Configuration", parent)

class LiveBindingSystem(QObject):
    """System for real-time parameter binding and hot-reload functionality"""

    # Signals
    parameter_updated = pyqtSignal(str, object)  # parameter_id, new_value
    system_reloaded = pyqtSignal()
    binding_error = pyqtSignal(str, str)  # parameter_id, error_message

    def __init__(self):
        super().__init__()
        self.trading_system_refs = {}
        self.config_managers = {}
        self.parameter_bindings = {}
        self.auto_save_enabled = True
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.save_all_configurations)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds

        # Initialize system references
        self._initialize_system_references()

    def _initialize_system_references(self):
        """Initialize references to trading system components"""
        try:
            # Import and store references to key system components
            from config.autonomous_config import AutonomousConfigManager
            from config.trading_config import ConfigManager

            self.config_managers['autonomous'] = AutonomousConfigManager()
            self.config_managers['trading'] = ConfigManager()

            # Also try to import config classes directly
            try:
                from config.trading_config import TradingConfig
                from config.autonomous_config import TradingConfig as AutoTradingConfig, RiskConfig, MLConfig

                # Store config classes for later instantiation
                self.config_classes = {
                    'TradingConfig': TradingConfig,
                    'AutoTradingConfig': AutoTradingConfig,
                    'RiskConfig': RiskConfig,
                    'MLConfig': MLConfig
                }

            except ImportError as e:
                print(f"⚠️ Could not import config classes: {e}")
                self.config_classes = {}

            print("✅ Live binding system initialized with config managers")

        except ImportError as e:
            print(f"⚠️ Could not initialize all config managers: {e}")
            self.config_managers = {}
            self.config_classes = {}

    def register_trading_system_component(self, component_name: str, component_ref):
        """Register a trading system component for live updates"""
        self.trading_system_refs[component_name] = component_ref
        print(f"📡 Registered trading system component: {component_name}")

    def bind_parameter(self, param_id: str, component_name: str, attribute_path: str,
                      transform_func: callable = None):
        """Bind a GUI parameter to a system component attribute"""
        self.parameter_bindings[param_id] = {
            'component_name': component_name,
            'attribute_path': attribute_path,
            'transform_func': transform_func
        }
        print(f"🔗 Bound parameter {param_id} to {component_name}.{attribute_path}")

    def update_parameter(self, param_id: str, new_value):
        """Update a parameter in the live trading system"""
        try:
            if param_id not in self.parameter_bindings:
                # Only warn for important parameters, not every discovered parameter
                if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                    print(f"⚠️ No binding found for parameter: {param_id}")
                return False

            binding = self.parameter_bindings[param_id]
            component_name = binding['component_name']
            attribute_path = binding['attribute_path']
            transform_func = binding.get('transform_func')

            # Get the component reference
            if component_name not in self.trading_system_refs:
                # Only warn for important components
                if component_name in ['trading_config', 'risk_manager', 'llm_orchestrator']:
                    print(f"⚠️ Component not registered: {component_name}")
                return False

            component = self.trading_system_refs[component_name]

            # Apply transformation if provided
            if transform_func:
                new_value = transform_func(new_value)

            # Update the attribute using dot notation
            self._set_nested_attribute(component, attribute_path, new_value)

            # Emit success signal
            self.parameter_updated.emit(param_id, new_value)

            # Only log successful updates for important parameters
            if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                print(f"✅ Updated {component_name}.{attribute_path} = {new_value}")
            return True

        except Exception as e:
            # Only show errors for important parameters
            if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                error_msg = f"Failed to update parameter {param_id}: {e}"
                print(f"❌ {error_msg}")
                self.binding_error.emit(param_id, error_msg)
            return False

    def _set_nested_attribute(self, obj, attribute_path: str, value):
        """Set nested attribute using dot notation (e.g., 'config.risk.max_leverage')"""
        attributes = attribute_path.split('.')
        current_obj = obj

        # Navigate to the parent object
        for attr in attributes[:-1]:
            if hasattr(current_obj, attr):
                current_obj = getattr(current_obj, attr)
            else:
                raise AttributeError(f"Object has no attribute '{attr}'")

        # Set the final attribute
        final_attr = attributes[-1]
        if hasattr(current_obj, final_attr):
            setattr(current_obj, final_attr, value)
        else:
            raise AttributeError(f"Object has no attribute '{final_attr}'")

    def reload_system_configuration(self):
        """Hot-reload system configuration and discover new parameters"""
        try:
            print("🔄 Starting system configuration reload...")

            # Reload configuration managers
            for manager_name, manager in self.config_managers.items():
                if hasattr(manager, 'load_config'):
                    manager.load_config()
                    print(f"✅ Reloaded {manager_name} configuration")

            # Re-discover parameters
            discovery = ParameterDiscovery()
            new_parameters = discovery.discover_all_parameters()

            print(f"🔍 Re-discovered {len(new_parameters)} parameters")

            # Emit reload signal
            self.system_reloaded.emit()

            return new_parameters

        except Exception as e:
            error_msg = f"Failed to reload system configuration: {e}"
            print(f"❌ {error_msg}")
            self.binding_error.emit("system_reload", error_msg)
            return {}

    def save_all_configurations(self):
        """Save all configuration changes to files"""
        if not self.auto_save_enabled:
            return

        try:
            saved_count = 0
            for manager_name, manager in self.config_managers.items():
                if hasattr(manager, 'save_config'):
                    manager.save_config()
                    saved_count += 1

            if saved_count > 0:
                print(f"💾 Auto-saved {saved_count} configuration files")

        except Exception as e:
            print(f"⚠️ Error during auto-save: {e}")

    def enable_auto_save(self, enabled: bool):
        """Enable or disable auto-save functionality"""
        self.auto_save_enabled = enabled
        if enabled:
            self.auto_save_timer.start(30000)
            print("✅ Auto-save enabled (30 second interval)")
        else:
            self.auto_save_timer.stop()
            print("⏸️ Auto-save disabled")

class HotReloadManager(QObject):
    """Manager for hot-reloading code and configuration changes"""

    # Signals
    reload_completed = pyqtSignal(dict)  # new_parameters
    reload_failed = pyqtSignal(str)  # error_message

    def __init__(self):
        super().__init__()
        self.file_watcher = QFileSystemWatcher()
        self.watched_files = []
        self.last_reload_time = datetime.now()
        self.reload_cooldown = 5  # seconds

        # Connect file watcher
        self.file_watcher.fileChanged.connect(self.on_file_changed)

        # Setup file watching
        self.setup_file_watching()

    def setup_file_watching(self):
        """Setup file system watching for configuration files"""
        config_files = [
            'config/autonomous_config.py',
            'config/trading_config.py',
            'config/config.yaml',
            'config/trading_config.yaml',
            'config/models_config.yaml',
            'config/unified_llm_config.yaml'
        ]

        for config_file in config_files:
            if os.path.exists(config_file):
                self.file_watcher.addPath(config_file)
                self.watched_files.append(config_file)
                print(f"👁️ Watching file: {config_file}")

    def on_file_changed(self, file_path: str):
        """Handle file change events"""
        # Implement cooldown to avoid rapid reloads
        now = datetime.now()
        if (now - self.last_reload_time).seconds < self.reload_cooldown:
            return

        print(f"📁 Configuration file changed: {file_path}")
        self.trigger_hot_reload()
        self.last_reload_time = now

    def trigger_hot_reload(self):
        """Trigger hot reload of configuration"""
        try:
            print("🔥 Triggering hot reload...")

            # Re-discover parameters
            discovery = ParameterDiscovery()
            new_parameters = discovery.discover_all_parameters()

            # Emit success signal
            self.reload_completed.emit(new_parameters)

            print(f"✅ Hot reload completed: {len(new_parameters)} parameters")

        except Exception as e:
            error_msg = f"Hot reload failed: {e}"
            print(f"❌ {error_msg}")
            self.reload_failed.emit(error_msg)

class ScalperGPTMainWindow(QMainWindow):
    """Main window for ScalperGPT dynamic GUI interface"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("ScalperGPT - Dynamic Trading Interface")
        self.setGeometry(100, 100, 1600, 1000)

        # Initialize core components
        self.parameter_discovery = ParameterDiscovery()
        self.live_binding = LiveBindingSystem()
        self.hot_reload = HotReloadManager()

        # Initialize responsive UI manager
        try:
            from gui.background_workers import ResponsiveUIManager
            self.ui_manager = ResponsiveUIManager()
        except ImportError:
            print("⚠️ Background workers not available")
            self.ui_manager = None

        # Configuration panels
        self.config_panels = {}
        self.discovered_parameters = {}

        # Trading system integration
        self.trading_interface = None

        # Setup UI
        self.setup_ui()
        self.setup_connections()
        self.discover_and_populate_parameters()

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Auto-connect to trading system on startup
        QTimer.singleShot(1000, self.auto_connect_trading_system)

        print("✅ ScalperGPT Main Window initialized")

    def setup_ui(self):
        """Setup the main user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # Header
        header = self.create_header()
        layout.addWidget(header)

        # Main content area
        main_splitter = QSplitter(Qt.Horizontal)

        # Left side: Configuration panels
        config_area = self.create_configuration_area()
        main_splitter.addWidget(config_area)

        # Right side: Dashboard and controls
        dashboard_area = self.create_dashboard_area()
        main_splitter.addWidget(dashboard_area)

        # Set splitter proportions (60% config, 40% dashboard)
        main_splitter.setSizes([960, 640])
        layout.addWidget(main_splitter)

        # Status bar
        self.setup_status_bar()

    def create_header(self) -> QWidget:
        """Create header with title and controls"""
        header = QWidget()
        layout = QHBoxLayout(header)
        layout.setContentsMargins(0, 0, 0, 0)

        # Title
        title_label = QLabel("🚀 SCALPERGPT DYNAMIC INTERFACE")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.LIGHT_GREEN};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
        """)

        # Control buttons
        controls_layout = QHBoxLayout()

        # Reload button
        reload_btn = QPushButton("🔄 Reload Code")
        reload_btn.setToolTip("Hot-reload configuration and discover new parameters")
        reload_btn.clicked.connect(self.trigger_hot_reload)

        # Save button
        save_btn = QPushButton("💾 Save Config")
        save_btn.setToolTip("Save all configuration changes")
        save_btn.clicked.connect(self.save_all_configurations)

        # Connect button
        connect_btn = QPushButton("🔗 Connect Trading")
        connect_btn.setToolTip("Connect to trading system")
        connect_btn.clicked.connect(self.connect_trading_system)

        controls_layout.addWidget(reload_btn)
        controls_layout.addWidget(save_btn)
        controls_layout.addWidget(connect_btn)

        layout.addWidget(title_label)
        layout.addStretch()
        layout.addLayout(controls_layout)

        return header

    def create_configuration_area(self) -> QWidget:
        """Create configuration panels area"""
        # Create tab widget for configuration panels
        self.config_tabs = QTabWidget()

        # Create configuration panels
        self.config_panels['symbols_data'] = SymbolsDataPanel()
        self.config_panels['llm_prompts'] = LLMPromptsPanel()
        self.config_panels['execution_rules'] = ExecutionRulesPanel()
        self.config_panels['risk_management'] = RiskManagementPanel()
        self.config_panels['monitoring_logging'] = MonitoringLoggingPanel()

        # Add panels to tabs
        self.config_tabs.addTab(self.config_panels['symbols_data'], "📊 Symbols & Data")
        self.config_tabs.addTab(self.config_panels['llm_prompts'], "🤖 LLM Prompts")
        self.config_tabs.addTab(self.config_panels['execution_rules'], "⚡ Execution")
        self.config_tabs.addTab(self.config_panels['risk_management'], "🛡️ Risk Mgmt")
        self.config_tabs.addTab(self.config_panels['monitoring_logging'], "📈 Monitoring")

        return self.config_tabs

    def create_dashboard_area(self) -> QWidget:
        """Create dashboard and controls area"""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)

        # Real-time dashboard
        try:
            from gui.scalper_dashboard import RealTimeDashboard
            self.dashboard = RealTimeDashboard()
            layout.addWidget(self.dashboard)
        except ImportError:
            # Fallback dashboard
            placeholder = QLabel("📊 Dashboard (Import Error)")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: 16px;")
            layout.addWidget(placeholder)
            self.dashboard = None

        return dashboard_widget

    def setup_status_bar(self):
        """Setup status bar with system information"""
        status_bar = self.statusBar()

        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        status_bar.addWidget(self.connection_status)

        # Parameter count
        self.param_count_label = QLabel("Parameters: 0")
        status_bar.addPermanentWidget(self.param_count_label)

        # Last update time
        self.last_update_label = QLabel("Last Update: Never")
        status_bar.addPermanentWidget(self.last_update_label)

    def setup_connections(self):
        """Setup signal connections"""
        # Connect live binding signals
        self.live_binding.parameter_updated.connect(self.on_parameter_updated)
        self.live_binding.binding_error.connect(self.on_binding_error)

        # Connect hot reload signals
        self.hot_reload.reload_completed.connect(self.on_hot_reload_completed)
        self.hot_reload.reload_failed.connect(self.on_hot_reload_failed)

        # Connect panel parameter changes
        for panel in self.config_panels.values():
            panel.parameter_changed.connect(self.on_panel_parameter_changed)

        # Connect dashboard signals if available
        if self.dashboard:
            self.dashboard.manual_trade_requested.connect(self.on_manual_trade_requested)
            self.dashboard.emergency_stop_requested.connect(self.on_emergency_stop_requested)

    def discover_and_populate_parameters(self):
        """Discover parameters and populate configuration panels"""
        print("🔍 Discovering and populating parameters...")

        # Discover all parameters
        self.discovered_parameters = self.parameter_discovery.discover_all_parameters()

        # Categorize and populate panels
        self.populate_configuration_panels()

        # Update status
        self.param_count_label.setText(f"Parameters: {len(self.discovered_parameters)}")
        self.last_update_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        print(f"✅ Populated {len(self.discovered_parameters)} parameters across {len(self.config_panels)} panels")

    def populate_configuration_panels(self):
        """Populate configuration panels with discovered parameters"""
        # Define parameter categories
        category_mapping = {
            'symbols_data': ['symbol', 'timeframe', 'data', 'scanner', 'market'],
            'llm_prompts': ['llm', 'temperature', 'token', 'prompt', 'model', 'ai'],
            'execution_rules': ['execution', 'order', 'trade', 'position', 'leverage'],
            'risk_management': ['risk', 'stop', 'loss', 'profit', 'limit', 'max'],
            'monitoring_logging': ['log', 'monitor', 'interval', 'update', 'health']
        }

        # Distribute parameters to panels
        bound_count = 0
        total_important = 0

        for param_id, param_info in self.discovered_parameters.items():
            panel_key = self.determine_panel_for_parameter(param_info, category_mapping)

            if panel_key in self.config_panels:
                self.config_panels[panel_key].add_parameter(param_id, param_info)

                # Setup live binding
                self.setup_parameter_binding(param_id, param_info)

                # Count important parameters
                if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                    total_important += 1
                    if param_id in self.live_binding.parameter_bindings:
                        bound_count += 1

        # Report binding status
        if total_important > 0:
            binding_percentage = (bound_count / total_important) * 100
            print(f"📊 Parameter binding status: {bound_count}/{total_important} important parameters bound ({binding_percentage:.1f}%)")

    def determine_panel_for_parameter(self, param_info: ParameterInfo, category_mapping: Dict) -> str:
        """Determine which panel a parameter belongs to"""
        param_name_lower = param_info.name.lower()
        param_category_lower = param_info.category.lower()

        # Check each category mapping
        for panel_key, keywords in category_mapping.items():
            for keyword in keywords:
                if keyword in param_name_lower or keyword in param_category_lower:
                    return panel_key

        # Default to symbols_data panel
        return 'symbols_data'

    def setup_parameter_binding(self, param_id: str, param_info: ParameterInfo):
        """Setup live binding for a parameter"""
        # Determine component and attribute path based on parameter
        component_name, attribute_path = self.determine_binding_target(param_info)

        if component_name and attribute_path:
            self.live_binding.bind_parameter(param_id, component_name, attribute_path)

            # Only log binding for important parameters
            if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                print(f"🔗 Bound parameter {param_id} to {component_name}.{attribute_path}")
        else:
            # Only warn about missing bindings for important parameters
            if any(keyword in param_id.lower() for keyword in ['trading', 'risk', 'llm', 'leverage', 'confidence']):
                print(f"⚠️ No binding target found for parameter: {param_id}")

    def determine_binding_target(self, param_info: ParameterInfo) -> tuple:
        """Determine binding target for a parameter"""
        param_name = param_info.name.lower()
        category = param_info.category.lower()

        # Machine Learning parameters
        if 'machine_learning' in category or 'ml_' in param_name:
            return ('ml_config', param_info.name.replace('machine_learning_', ''))

        # Risk management parameters
        elif 'risk' in category:
            return ('risk_config', param_info.name.replace('risk_management_', ''))

        # Main trading config parameters
        elif 'main_trading' in param_name:
            return ('trading_config', param_info.name.replace('main_trading_', ''))

        # Auto trading config parameters
        elif 'trading' in category and 'autonomous' not in category:
            return ('auto_trading_config', param_info.name.replace('trading_', ''))

        # LLM parameters
        elif 'llm' in category or 'llama' in param_name or 'lmstudio' in param_name:
            return ('llm_orchestrator', f'config.{param_info.name}')

        # YAML config parameters - map to appropriate config objects
        elif '.yaml_' in param_info.name:
            config_file = param_info.name.split('.yaml_')[0]
            param_path = param_info.name.split('.yaml_')[1]

            if 'trading_config' in config_file:
                return ('trading_config', param_path)
            elif 'unified_llm_config' in config_file:
                return ('llm_orchestrator', f'config.{param_path}')
            elif 'models_config' in config_file:
                return ('llm_orchestrator', f'config.{param_path}')

        # Default fallback
        return (None, None)

    # Event handlers
    def on_parameter_updated(self, param_id: str, new_value):
        """Handle parameter update from live binding"""
        print(f"✅ Parameter updated: {param_id} = {new_value}")

    def on_binding_error(self, param_id: str, error_message: str):
        """Handle binding error"""
        print(f"❌ Binding error for {param_id}: {error_message}")
        self.statusBar().showMessage(f"Error: {error_message}", 5000)

    def on_hot_reload_completed(self, new_parameters: Dict):
        """Handle hot reload completion"""
        print(f"🔥 Hot reload completed: {len(new_parameters)} parameters")
        self.discovered_parameters = new_parameters
        self.populate_configuration_panels()
        self.param_count_label.setText(f"Parameters: {len(new_parameters)}")
        self.statusBar().showMessage("Hot reload completed", 3000)

    def on_hot_reload_failed(self, error_message: str):
        """Handle hot reload failure"""
        print(f"❌ Hot reload failed: {error_message}")
        self.statusBar().showMessage(f"Hot reload failed: {error_message}", 5000)

    def on_panel_parameter_changed(self, param_id: str, new_value):
        """Handle parameter change from configuration panel"""
        # Update live system
        success = self.live_binding.update_parameter(param_id, new_value)

        if success:
            print(f"🔄 Live update: {param_id} = {new_value}")
        else:
            print(f"⚠️ Failed to update: {param_id}")

    def on_manual_trade_requested(self, trade_params: Dict):
        """Handle manual trade request from dashboard"""
        print(f"📈 Manual trade requested: {trade_params}")

        # Execute manual trade through trading interface
        if self.trading_interface:
            try:
                # This would integrate with your actual trading interface
                result = self.execute_manual_trade(trade_params)
                if result:
                    self.statusBar().showMessage("Manual trade executed", 3000)
                else:
                    self.statusBar().showMessage("Manual trade failed", 5000)
            except Exception as e:
                print(f"❌ Manual trade error: {e}")
                self.statusBar().showMessage(f"Trade error: {e}", 5000)

    def on_emergency_stop_requested(self):
        """Handle emergency stop request"""
        print("🚨 EMERGENCY STOP REQUESTED")

        # Trigger emergency stop in trading system
        if self.trading_interface:
            try:
                self.trading_interface.emergency_stop()
                self.statusBar().showMessage("EMERGENCY STOP ACTIVATED", 10000)
            except Exception as e:
                print(f"❌ Emergency stop error: {e}")

    # Action methods
    def trigger_hot_reload(self):
        """Trigger hot reload of configuration"""
        print("🔄 Triggering hot reload...")
        self.hot_reload.trigger_hot_reload()

    def save_all_configurations(self):
        """Save all configuration changes"""
        print("💾 Saving all configurations...")
        self.live_binding.save_all_configurations()
        self.statusBar().showMessage("Configurations saved", 3000)

    def connect_trading_system(self):
        """Connect to trading system"""
        print("🔗 Connecting to trading system...")

        try:
            # Import the main trading system components
            from launch_epinnox import (
                EpinnoxTradingInterface, exchange,
                autonomous_trading_orchestrator, llm_orchestrator,
                risk_management_system, unified_execution_engine
            )

            # Get or create trading interface instance
            if hasattr(EpinnoxTradingInterface, '_instance') and EpinnoxTradingInterface._instance:
                self.trading_interface = EpinnoxTradingInterface._instance
            else:
                # Initialize new instance if needed
                self.trading_interface = EpinnoxTradingInterface()

            # Register all components with live binding system
            self.live_binding.register_trading_system_component('main_interface', self.trading_interface)
            self.live_binding.register_trading_system_component('exchange', exchange)

            if autonomous_trading_orchestrator:
                self.live_binding.register_trading_system_component('orchestrator', autonomous_trading_orchestrator)
            if llm_orchestrator:
                self.live_binding.register_trading_system_component('llm_orchestrator', llm_orchestrator)
            if risk_management_system:
                self.live_binding.register_trading_system_component('risk_manager', risk_management_system)
            if unified_execution_engine:
                self.live_binding.register_trading_system_component('execution_engine', unified_execution_engine)

            # Register configuration objects
            try:
                from config.trading_config import TradingConfig
                from config.autonomous_config import (
                    TradingConfig as AutoTradingConfig, RiskConfig, MLConfig
                )

                # Create config instances and register them
                trading_config = TradingConfig()
                auto_trading_config = AutoTradingConfig()
                risk_config = RiskConfig()
                ml_config = MLConfig()

                self.live_binding.register_trading_system_component('trading_config', trading_config)
                self.live_binding.register_trading_system_component('auto_trading_config', auto_trading_config)
                self.live_binding.register_trading_system_component('risk_config', risk_config)
                self.live_binding.register_trading_system_component('ml_config', ml_config)

                print("✅ Configuration objects registered")

            except ImportError as e:
                print(f"⚠️ Could not import some config classes: {e}")

            # Update UI manager with trading interface
            if self.ui_manager:
                self.ui_manager.set_trading_interface(self.trading_interface)

            # Set default symbol if available - EMERGENCY CONFIGURATION
            if hasattr(self.trading_interface, 'current_symbol'):
                default_symbol = self.trading_interface.current_symbol or "PEPE/USDT:USDT"
            else:
                default_symbol = "PEPE/USDT:USDT"  # 🚨 EMERGENCY SWITCH: PEPE viable with $19.51

            # Update dashboard with default symbol
            if self.dashboard:
                self.dashboard.symbol_combo.setCurrentText(default_symbol)
                print(f"🎯 Set default symbol: {default_symbol}")

            # Update connection status
            self.connection_status.setText("🟢 Connected")
            self.statusBar().showMessage("Trading system connected", 3000)

            print("✅ Trading system connected with all components")
            print(f"📊 Default symbol set to: {default_symbol}")

        except Exception as e:
            print(f"❌ Connection failed: {e}")
            self.connection_status.setText("🔴 Connection Failed")
            self.statusBar().showMessage(f"Connection failed: {e}", 5000)

    def auto_connect_trading_system(self):
        """Auto-connect to trading system on startup"""
        print("🔄 Auto-connecting to trading system...")
        self.connect_trading_system()

        # Start autonomous trading if enabled
        if self.trading_interface:
            try:
                # Check if autonomous mode should be enabled
                autonomous_enabled = True  # Default to enabled for scalping

                if autonomous_enabled:
                    print("🤖 Starting autonomous trading mode...")

                    # Start the trading system components
                    if hasattr(self.trading_interface, 'start_autonomous_trading'):
                        self.trading_interface.start_autonomous_trading()

                    self.statusBar().showMessage("🤖 Autonomous trading started", 5000)
                    print("✅ Autonomous trading mode activated")

            except Exception as e:
                print(f"⚠️ Could not start autonomous trading: {e}")
                self.statusBar().showMessage(f"Autonomous start failed: {e}", 5000)

    def execute_manual_trade(self, trade_params: Dict) -> bool:
        """Execute manual trade"""
        # This is a placeholder - integrate with your actual trading system
        action = trade_params.get('action')
        symbol = trade_params.get('symbol')
        size = trade_params.get('size')
        price = trade_params.get('price')

        print(f"🎯 Executing {action} trade: {size} {symbol} @ {price}")

        # Simulate trade execution
        return True

    def closeEvent(self, event):
        """Handle application close"""
        print("🔄 Shutting down ScalperGPT GUI...")

        # Shutdown background workers
        if self.ui_manager:
            self.ui_manager.shutdown()

        # Save configurations
        self.save_all_configurations()

        print("✅ ScalperGPT GUI shutdown complete")
        event.accept()

def run_scalper_gui():
    """Run the ScalperGPT GUI application"""
    print("🚀 Starting ScalperGPT Dynamic GUI Interface...")

    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("ScalperGPT Dynamic Interface")
    app.setApplicationVersion("1.0")

    # Create and show main window
    main_window = ScalperGPTMainWindow()
    main_window.show()

    print("✅ ScalperGPT GUI started successfully")

    # Run application
    return app.exec_()

if __name__ == "__main__":
    # Test parameter discovery if run directly
    if len(sys.argv) > 1 and sys.argv[1] == "--test-discovery":
        print("🧪 Testing parameter discovery...")
        discovery = ParameterDiscovery()
        params = discovery.discover_all_parameters()

        print(f"\n📊 Parameter Discovery Results:")
        print(f"Total parameters discovered: {len(params)}")

        # Group by category
        categories = {}
        for param_id, param_info in params.items():
            category = param_info.category
            if category not in categories:
                categories[category] = []
            categories[category].append(param_info)

        for category, param_list in categories.items():
            print(f"\n📁 {category}: {len(param_list)} parameters")
            for param in param_list[:3]:  # Show first 3 as examples
                print(f"  • {param.name} ({param.param_type.__name__}) = {param.default_value}")
    else:
        # Run the full GUI application
        sys.exit(run_scalper_gui())
    # Test parameter discovery
    discovery = ParameterDiscovery()
    params = discovery.discover_all_parameters()

    print(f"\n📊 Parameter Discovery Results:")
    print(f"Total parameters discovered: {len(params)}")

    # Group by category
    categories = {}
    for param_id, param_info in params.items():
        category = param_info.category
        if category not in categories:
            categories[category] = []
        categories[category].append(param_info)

    for category, param_list in categories.items():
        print(f"\n📁 {category}: {len(param_list)} parameters")
        for param in param_list[:3]:  # Show first 3 as examples
            print(f"  • {param.name} ({param.param_type.__name__}) = {param.default_value}")
