#!/usr/bin/env python3
"""
Emergency Autonomous Trading Monitor
Real-time monitoring of PEPE/USDT:USDT autonomous trading performance
"""

import sys
import os
import json
import time
import threading
from datetime import datetime, timedelta
from collections import defaultdict

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class AutonomousTradingMonitor:
    """Real-time monitor for autonomous trading performance"""
    
    def __init__(self):
        self.monitoring_active = False
        self.start_time = None
        self.session_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'current_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'avg_trade_duration': 0.0,
            'emergency_stops': 0,
            'quality_violations': 0,
            'system_uptime': 0.0
        }
        
        self.trade_history = []
        self.performance_log = []
        self.alert_conditions = {
            'max_daily_loss': 0.98,  # $0.98 (5% of $19.51)
            'max_consecutive_losses': 3,
            'min_win_rate': 0.40,  # 40% minimum win rate
            'max_drawdown_pct': 0.10,  # 10% max drawdown
            'quality_threshold_violations': 5
        }
        
        self.consecutive_losses = 0
        self.last_balance_check = 19.51
        
    def start_monitoring(self):
        """Start real-time monitoring"""
        print("🔄 STARTING AUTONOMOUS TRADING MONITOR")
        print("=" * 60)
        
        self.monitoring_active = True
        self.start_time = datetime.now()
        
        print(f"⏰ Monitor Start: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Target Symbol: PEPE/USDT:USDT")
        print(f"💰 Account Balance: $19.51")
        print(f"🛡️ Emergency Stop Threshold: ${self.alert_conditions['max_daily_loss']:.2f}")
        
        # Start monitoring threads
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()
        
        # Start performance reporting
        self._performance_reporting_loop()
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        loop_count = 0
        
        while self.monitoring_active:
            try:
                loop_count += 1
                current_time = datetime.now()
                
                # Update system uptime
                if self.start_time:
                    uptime_seconds = (current_time - self.start_time).total_seconds()
                    self.session_metrics['system_uptime'] = uptime_seconds / 3600  # Hours
                
                # Simulate monitoring data (in real deployment, this would fetch actual data)
                self._simulate_monitoring_data(loop_count)
                
                # Check alert conditions
                self._check_alert_conditions()
                
                # Log performance metrics
                self._log_performance_metrics(current_time)
                
                # Wait for next monitoring cycle
                time.sleep(30)  # 30-second monitoring intervals
                
            except Exception as e:
                print(f"❌ Monitoring error: {e}")
                time.sleep(5)
    
    def _simulate_monitoring_data(self, loop_count):
        """Simulate monitoring data for demonstration"""
        import random
        
        # Simulate market conditions
        market_conditions = {
            'pepe_price': round(random.uniform(0.000007, 0.000009), 8),
            'spread_pct': round(random.uniform(0.5, 2.0), 2),
            'volume_24h': random.randint(1000000, 5000000),
            'volatility': round(random.uniform(0.02, 0.08), 3)
        }
        
        # Simulate ScalperGPT analysis
        scalper_analysis = {
            'spread_quality': round(random.uniform(6.0, 9.5), 1),
            'decision_quality': round(random.uniform(7.0, 9.0), 1),
            'signal': random.choice(['LONG', 'SHORT', 'WAIT']),
            'confidence': round(random.uniform(0.6, 0.95), 2)
        }
        
        # Simulate trade execution (occasionally)
        if loop_count % 5 == 0 and scalper_analysis['signal'] != 'WAIT':
            self._simulate_trade_execution(scalper_analysis, market_conditions)
        
        # Store current monitoring data
        self.current_market_data = market_conditions
        self.current_scalper_data = scalper_analysis
    
    def _simulate_trade_execution(self, scalper_analysis, market_conditions):
        """Simulate trade execution for demonstration"""
        import random
        
        # Simulate trade outcome
        trade_successful = random.random() > 0.4  # 60% success rate
        
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'symbol': 'PEPE/USDT:USDT',
            'side': scalper_analysis['signal'],
            'entry_price': market_conditions['pepe_price'],
            'position_size': 15.61,  # 80% of balance
            'contracts': int(15.61 / market_conditions['pepe_price']),
            'successful': trade_successful,
            'pnl': 0.0,
            'duration_minutes': random.randint(2, 15)
        }
        
        if trade_successful:
            # Simulate profit (2-4% gain)
            profit_pct = random.uniform(0.02, 0.04)
            trade_record['pnl'] = trade_record['position_size'] * profit_pct
            trade_record['exit_price'] = trade_record['entry_price'] * (1 + profit_pct)
            
            self.session_metrics['winning_trades'] += 1
            self.consecutive_losses = 0
        else:
            # Simulate loss (1-2% loss due to stop loss)
            loss_pct = random.uniform(0.01, 0.02)
            trade_record['pnl'] = -trade_record['position_size'] * loss_pct
            trade_record['exit_price'] = trade_record['entry_price'] * (1 - loss_pct)
            
            self.session_metrics['losing_trades'] += 1
            self.consecutive_losses += 1
        
        # Update session metrics
        self.session_metrics['total_trades'] += 1
        self.session_metrics['total_pnl'] += trade_record['pnl']
        
        # Calculate win rate
        if self.session_metrics['total_trades'] > 0:
            self.session_metrics['win_rate'] = self.session_metrics['winning_trades'] / self.session_metrics['total_trades']
        
        # Update drawdown
        if trade_record['pnl'] < 0:
            self.session_metrics['current_drawdown'] += abs(trade_record['pnl'])
            self.session_metrics['max_drawdown'] = max(
                self.session_metrics['max_drawdown'], 
                self.session_metrics['current_drawdown']
            )
        else:
            self.session_metrics['current_drawdown'] = max(0, self.session_metrics['current_drawdown'] - trade_record['pnl'])
        
        self.trade_history.append(trade_record)
        
        print(f"\n💰 TRADE EXECUTED:")
        print(f"  {trade_record['side']} {trade_record['contracts']:,} PEPE @ ${trade_record['entry_price']:.8f}")
        print(f"  PnL: ${trade_record['pnl']:+.2f} ({'✅ WIN' if trade_successful else '❌ LOSS'})")
    
    def _check_alert_conditions(self):
        """Check for alert conditions and emergency stops"""
        alerts = []
        
        # Check daily loss limit
        if abs(self.session_metrics['total_pnl']) >= self.alert_conditions['max_daily_loss']:
            alerts.append(f"🚨 DAILY LOSS LIMIT EXCEEDED: ${abs(self.session_metrics['total_pnl']):.2f}")
        
        # Check consecutive losses
        if self.consecutive_losses >= self.alert_conditions['max_consecutive_losses']:
            alerts.append(f"🚨 CONSECUTIVE LOSSES: {self.consecutive_losses}")
        
        # Check win rate
        if (self.session_metrics['total_trades'] >= 5 and 
            self.session_metrics['win_rate'] < self.alert_conditions['min_win_rate']):
            alerts.append(f"🚨 LOW WIN RATE: {self.session_metrics['win_rate']:.1%}")
        
        # Check drawdown
        drawdown_pct = self.session_metrics['max_drawdown'] / 19.51
        if drawdown_pct >= self.alert_conditions['max_drawdown_pct']:
            alerts.append(f"🚨 HIGH DRAWDOWN: {drawdown_pct:.1%}")
        
        # Check quality violations
        if hasattr(self, 'current_scalper_data'):
            if (self.current_scalper_data['spread_quality'] < 7.0 or 
                self.current_scalper_data['decision_quality'] < 8.0):
                self.session_metrics['quality_violations'] += 1
                
                if self.session_metrics['quality_violations'] >= self.alert_conditions['quality_threshold_violations']:
                    alerts.append(f"🚨 QUALITY VIOLATIONS: {self.session_metrics['quality_violations']}")
        
        # Display alerts
        for alert in alerts:
            print(f"\n{alert}")
            
        # Trigger emergency stop if critical alerts
        critical_alerts = [alert for alert in alerts if "DAILY LOSS" in alert or "CONSECUTIVE LOSSES" in alert]
        if critical_alerts:
            self._trigger_emergency_stop(critical_alerts)
    
    def _trigger_emergency_stop(self, reasons):
        """Trigger emergency stop"""
        self.session_metrics['emergency_stops'] += 1
        
        print(f"\n🛑 EMERGENCY STOP TRIGGERED!")
        print(f"📋 Reasons:")
        for reason in reasons:
            print(f"  - {reason}")
        
        print(f"\n🚨 EMERGENCY ACTIONS REQUIRED:")
        print(f"  1. Disable autonomous trading immediately")
        print(f"  2. Close all open positions")
        print(f"  3. Review trading performance and logs")
        print(f"  4. Adjust parameters before restarting")
        
        # In real implementation, this would actually stop trading
        print(f"  ⚠️ [SIMULATION MODE - No actual trading stopped]")
    
    def _log_performance_metrics(self, timestamp):
        """Log performance metrics"""
        metrics_entry = {
            'timestamp': timestamp.isoformat(),
            'session_metrics': self.session_metrics.copy(),
            'market_data': getattr(self, 'current_market_data', {}),
            'scalper_data': getattr(self, 'current_scalper_data', {})
        }
        
        self.performance_log.append(metrics_entry)
        
        # Keep only last 100 entries
        if len(self.performance_log) > 100:
            self.performance_log = self.performance_log[-100:]
    
    def _performance_reporting_loop(self):
        """Performance reporting loop"""
        try:
            while self.monitoring_active:
                self._display_performance_report()
                time.sleep(60)  # Report every minute
                
        except KeyboardInterrupt:
            print(f"\n⏹️ Monitoring stopped by user")
            self.stop_monitoring()
    
    def _display_performance_report(self):
        """Display real-time performance report"""
        current_time = datetime.now()
        
        print(f"\n📊 AUTONOMOUS TRADING PERFORMANCE REPORT")
        print(f"⏰ {current_time.strftime('%Y-%m-%d %H:%M:%S')} | Uptime: {self.session_metrics['system_uptime']:.1f}h")
        print("=" * 60)
        
        # Trading Performance
        print(f"💰 TRADING PERFORMANCE:")
        print(f"  Total Trades: {self.session_metrics['total_trades']}")
        print(f"  Win Rate: {self.session_metrics['win_rate']:.1%} ({self.session_metrics['winning_trades']}W/{self.session_metrics['losing_trades']}L)")
        print(f"  Total PnL: ${self.session_metrics['total_pnl']:+.2f}")
        print(f"  Max Drawdown: ${self.session_metrics['max_drawdown']:.2f}")
        print(f"  Consecutive Losses: {self.consecutive_losses}")
        
        # Market Conditions
        if hasattr(self, 'current_market_data'):
            market = self.current_market_data
            print(f"\n📈 MARKET CONDITIONS:")
            print(f"  PEPE Price: ${market.get('pepe_price', 0):.8f}")
            print(f"  Spread: {market.get('spread_pct', 0):.2f}%")
            print(f"  Volatility: {market.get('volatility', 0):.1%}")
        
        # ScalperGPT Status
        if hasattr(self, 'current_scalper_data'):
            scalper = self.current_scalper_data
            print(f"\n🤖 SCALPERGPT STATUS:")
            print(f"  Spread Quality: {scalper.get('spread_quality', 0):.1f}/10")
            print(f"  Decision Quality: {scalper.get('decision_quality', 0):.1f}/10")
            print(f"  Current Signal: {scalper.get('signal', 'UNKNOWN')}")
            print(f"  Confidence: {scalper.get('confidence', 0):.1%}")
        
        # Risk Management
        daily_loss_pct = abs(self.session_metrics['total_pnl']) / 19.51 * 100
        print(f"\n🛡️ RISK MANAGEMENT:")
        print(f"  Daily Loss: ${abs(self.session_metrics['total_pnl']):.2f} ({daily_loss_pct:.1f}% of balance)")
        print(f"  Loss Limit: ${self.alert_conditions['max_daily_loss']:.2f} (5% of balance)")
        print(f"  Emergency Stops: {self.session_metrics['emergency_stops']}")
        print(f"  Quality Violations: {self.session_metrics['quality_violations']}")
        
        # System Status
        status_color = "🟢" if self.session_metrics['emergency_stops'] == 0 else "🔴"
        print(f"\n{status_color} SYSTEM STATUS: {'ACTIVE' if self.monitoring_active else 'STOPPED'}")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring_active = False
        
        print(f"\n🏁 MONITORING STOPPED")
        print(f"📊 Final Session Summary:")
        print(f"  Duration: {self.session_metrics['system_uptime']:.1f} hours")
        print(f"  Total Trades: {self.session_metrics['total_trades']}")
        print(f"  Final PnL: ${self.session_metrics['total_pnl']:+.2f}")
        print(f"  Win Rate: {self.session_metrics['win_rate']:.1%}")
        
        # Save session data
        session_data = {
            'session_end': datetime.now().isoformat(),
            'session_metrics': self.session_metrics,
            'trade_history': self.trade_history,
            'performance_log': self.performance_log[-10:]  # Last 10 entries
        }
        
        filename = f"autonomous_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        print(f"💾 Session data saved to: {filename}")

if __name__ == "__main__":
    print("🔄 STARTING AUTONOMOUS TRADING MONITOR")
    
    monitor = AutonomousTradingMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        monitor.stop_monitoring()
    except Exception as e:
        print(f"❌ Monitor error: {e}")
        monitor.stop_monitoring()
