#!/usr/bin/env python3
"""
Test if place_limit_order method exists in TradingSystemInterface
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_method_exists():
    """Test if the place_limit_order method exists"""
    print("🔍 Testing place_limit_order method existence...")
    
    try:
        # Import the trading interface
        from trading.trading_system_interface import TradingSystemInterface
        
        # Create instance
        interface = TradingSystemInterface()
        
        # Check if method exists
        has_method = hasattr(interface, 'place_limit_order')
        print(f"  Has place_limit_order method: {'✅ YES' if has_method else '❌ NO'}")
        
        if has_method:
            # Check if it's callable
            is_callable = callable(getattr(interface, 'place_limit_order'))
            print(f"  Method is callable: {'✅ YES' if is_callable else '❌ NO'}")
            
            # Test method signature
            import inspect
            sig = inspect.signature(interface.place_limit_order)
            print(f"  Method signature: {sig}")
            
            # Test calling the method
            try:
                result = interface.place_limit_order(
                    symbol="PEPE/USDT:USDT",
                    side="buy",
                    amount=1000000,
                    price=0.00001325
                )
                print(f"  Method call result: {'✅ SUCCESS' if result else '❌ FAILED'}")
                if result:
                    print(f"    Order ID: {result.get('id', 'NO_ID')}")
                    print(f"    Status: {result.get('status', 'NO_STATUS')}")
                
                return True
                
            except Exception as e:
                print(f"  ❌ Method call failed: {e}")
                return False
        else:
            # List available methods
            methods = [method for method in dir(interface) if not method.startswith('_')]
            print(f"  Available methods: {methods[:10]}...")  # Show first 10
            return False
            
    except Exception as e:
        print(f"❌ Error testing method: {e}")
        return False

if __name__ == "__main__":
    success = test_method_exists()
    print(f"\nTest result: {'✅ PASSED' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)
