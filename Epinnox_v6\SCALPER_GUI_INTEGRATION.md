# ScalperGPT Dynamic GUI Integration Guide

## 🚀 Overview

The ScalperGPT Dynamic GUI is a comprehensive PyQt5-based interface that automatically discovers and exposes all configurable parameters from the Epinnox v6 trading system. It provides real-time parameter updates, manual trading controls, and hot-reload functionality.

## 📁 File Structure

```
Epinnox_v6/
├── scalper_gui.py                    # Main GUI entry point
├── gui/
│   ├── scalper_dashboard.py          # Real-time dashboard
│   ├── background_workers.py         # Background processing
│   └── matrix_theme.py               # Matrix theme styling
├── test_scalper_gui.py               # Integration tests
└── SCALPER_GUI_INTEGRATION.md        # This guide
```

## 🔧 Core Components

### 1. Parameter Discovery System
- **ParameterDiscovery**: Automatically scans codebase for configurable parameters
- **Sources**: Dataclasses, YAML configs, Python modules
- **Categories**: Trading, Risk Management, LLM, Execution, Monitoring

### 2. Dynamic UI Generation
- **WidgetFactory**: Creates appropriate widgets based on parameter types
- **Widget Types**: Checkboxes, sliders, spinboxes, comboboxes, text fields
- **Auto-validation**: Type checking and constraint enforcement

### 3. Configuration Panels
- **Symbols & Data**: Symbol lists, timeframes, data sources
- **LLM Prompts**: Model settings, temperature, tokens, prompts
- **Execution Rules**: Order types, position limits, leverage
- **Risk Management**: Stop loss, take profit, risk limits
- **Monitoring & Logging**: Log levels, update intervals, health metrics

### 4. Live Binding System
- **Real-time Updates**: Changes immediately update running system
- **Hot-reload**: Rescans codebase for new parameters
- **Auto-save**: Periodic configuration saving

### 5. Real-time Dashboard
- **Position Monitoring**: Current positions with live PnL
- **Order Management**: Open orders with status tracking
- **System Health**: CPU, memory, API latency, connection status
- **Manual Controls**: Force entry/exit, emergency stop

### 6. Background Processing
- **Data Refresh Worker**: Non-blocking market data updates
- **LLM Analysis Worker**: Background AI analysis
- **Task Scheduler**: Priority-based task execution

## 🚀 Quick Start

### 1. Basic Launch
```bash
# Run the GUI
python scalper_gui.py

# Test parameter discovery only
python scalper_gui.py --test-discovery
```

### 2. Integration with Existing System
```python
# In your trading system
from scalper_gui import run_scalper_gui

# Launch GUI
run_scalper_gui()
```

### 3. Connect to Trading System
1. Click "🔗 Connect Trading" button
2. GUI will automatically detect EpinnoxTradingInterface
3. Live parameter binding will be established

## 🔗 Integration Points

### 1. Trading System Integration
```python
# Register your trading components
live_binding.register_trading_system_component("orchestrator", orchestrator)
live_binding.register_trading_system_component("risk_manager", risk_manager)
live_binding.register_trading_system_component("execution_engine", execution_engine)
```

### 2. Parameter Binding
```python
# Bind GUI parameters to system components
live_binding.bind_parameter(
    param_id="risk_max_leverage",
    component_name="risk_manager", 
    attribute_path="config.max_leverage"
)
```

### 3. Manual Trading Integration
```python
# Handle manual trade requests
def on_manual_trade_requested(trade_params):
    symbol = trade_params['symbol']
    action = trade_params['action']  # 'long', 'short', 'close_all'
    size = trade_params['size']
    price = trade_params['price']
    
    # Execute through your trading interface
    result = trading_interface.execute_manual_trade(action, symbol, size, price)
    return result
```

### 4. Emergency Stop Integration
```python
# Handle emergency stop requests
def on_emergency_stop_requested():
    # Trigger your emergency stop system
    emergency_coordinator.trigger_emergency_stop(
        EmergencyType.MANUAL_OVERRIDE,
        EmergencyLevel.CRITICAL,
        "GUI emergency stop requested"
    )
```

## 🧪 Testing

### 1. Run Integration Tests
```bash
# Full test suite
python test_scalper_gui.py

# Quick validation
python test_scalper_gui.py --validate
```

### 2. Test Coverage
- Parameter discovery from all sources
- Widget creation for all parameter types
- Live binding functionality
- Error handling and edge cases
- GUI component integration

### 3. Manual Testing Checklist
- [ ] GUI launches without errors
- [ ] All configuration panels load
- [ ] Parameters are discovered and displayed
- [ ] Widget interactions work correctly
- [ ] Live parameter updates function
- [ ] Hot-reload discovers new parameters
- [ ] Trading system connection works
- [ ] Manual trading controls respond
- [ ] Emergency stop functions
- [ ] Dashboard updates in real-time

## 🔧 Configuration

### 1. Parameter Categories
The system automatically categorizes parameters based on keywords:

```python
category_mapping = {
    'symbols_data': ['symbol', 'timeframe', 'data', 'scanner', 'market'],
    'llm_prompts': ['llm', 'temperature', 'token', 'prompt', 'model', 'ai'],
    'execution_rules': ['execution', 'order', 'trade', 'position', 'leverage'],
    'risk_management': ['risk', 'stop', 'loss', 'profit', 'limit', 'max'],
    'monitoring_logging': ['log', 'monitor', 'interval', 'update', 'health']
}
```

### 2. Widget Type Selection
Widgets are automatically selected based on parameter properties:

- **Boolean** → Checkbox
- **Choices** → ComboBox  
- **Numeric with range** → Slider + SpinBox
- **Integer** → SpinBox
- **Float** → DoubleSpinBox
- **List** → List widget with add/remove
- **String** → LineEdit

### 3. Constraint Inference
The system infers constraints from parameter names:

- `*_pct`, `*_percent` → 0.0 to 1.0 range
- `confidence`, `threshold` → 0.0 to 1.0 range
- `leverage` → 1.0 to 100.0 range
- `temperature` → 0.0 to 2.0 range
- `*_token*` → 1 to 4096 range
- `interval`, `delay` → 0.1 to 3600.0 seconds

## 🎨 Customization

### 1. Adding New Parameter Sources
```python
class CustomParameterDiscovery(ParameterDiscovery):
    def discover_all_parameters(self):
        super().discover_all_parameters()
        self._discover_from_custom_source()
        return self.discovered_parameters
    
    def _discover_from_custom_source(self):
        # Add your custom parameter discovery logic
        pass
```

### 2. Custom Widget Types
```python
class CustomWidgetFactory(WidgetFactory):
    @staticmethod
    def create_widget(param_info):
        if param_info.name == "custom_param":
            return CustomWidget(param_info)
        return WidgetFactory.create_widget(param_info)
```

### 3. Custom Themes
```python
# Extend MatrixTheme for custom styling
class CustomTheme(MatrixTheme):
    PRIMARY_COLOR = "#FF6600"  # Custom orange
    
    @classmethod
    def get_stylesheet(cls):
        # Return custom stylesheet
        pass
```

## 🚨 Error Handling

### 1. Common Issues
- **PyQt5 not installed**: Install with `pip install PyQt5 pyqtgraph`
- **Import errors**: Ensure all dependencies are available
- **Parameter binding failures**: Check component registration
- **Widget creation errors**: Verify parameter type compatibility

### 2. Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run with debug output
python scalper_gui.py
```

### 3. Fallback Behavior
- Missing config files are handled gracefully
- Failed parameter bindings don't crash the system
- Widget creation errors fall back to text input
- Connection failures allow retry

## 📈 Performance

### 1. Background Processing
- Data refresh runs in separate thread
- LLM analysis doesn't block UI
- Task scheduling prevents overload

### 2. Update Optimization
- Batched GUI updates prevent flickering
- Configurable refresh intervals
- Lazy loading of heavy components

### 3. Memory Management
- Automatic cleanup of background workers
- Efficient parameter caching
- Resource cleanup on shutdown

## 🔒 Security

### 1. Parameter Validation
- Type checking on all inputs
- Range validation for numeric values
- Sanitization of string inputs

### 2. Trading Safety
- Emergency stop always available
- Manual trade confirmation
- Risk limit enforcement

### 3. Configuration Safety
- Auto-backup before changes
- Rollback capability
- Change logging

## 📚 API Reference

### Main Classes
- `ScalperGPTMainWindow`: Main application window
- `ParameterDiscovery`: Parameter discovery system
- `WidgetFactory`: Dynamic widget creation
- `LiveBindingSystem`: Real-time parameter binding
- `RealTimeDashboard`: Trading dashboard
- `ResponsiveUIManager`: Background processing

### Key Methods
- `discover_all_parameters()`: Scan for parameters
- `create_widget(param_info)`: Create appropriate widget
- `bind_parameter(id, component, path)`: Bind parameter
- `update_parameter(id, value)`: Update live system
- `trigger_hot_reload()`: Reload configuration

## 🤝 Contributing

1. Follow the existing code structure
2. Add tests for new functionality
3. Update documentation
4. Ensure Matrix theme compatibility
5. Test with real trading system

## 📞 Support

For issues or questions:
1. Check the integration tests
2. Review error logs
3. Validate parameter discovery
4. Test with minimal configuration
5. Check PyQt5 compatibility
