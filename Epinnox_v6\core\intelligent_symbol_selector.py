#!/usr/bin/env python3
"""
Intelligent Symbol Selector
Smart auto symbol selection based on multiple factors for optimal trading opportunities
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class SymbolMetrics:
    """Comprehensive metrics for symbol evaluation"""
    symbol: str
    price: float
    volume_24h: float
    volatility: float
    spread_pct: float
    liquidity_score: float
    trend_strength: float
    momentum_score: float
    min_order_value: float
    max_leverage: float
    trading_fees: float
    opportunity_score: float
    risk_score: float
    final_score: float
    reasoning: str

class IntelligentSymbolSelector:
    """Intelligent symbol selection system for optimal trading opportunities"""
    
    def __init__(self, account_balance: float = 19.51):
        """
        Initialize intelligent symbol selector
        
        Args:
            account_balance: Current account balance
        """
        self.account_balance = account_balance
        
        # 🎯 SYMBOL UNIVERSE: Comprehensive symbol pool
        self.symbol_universe = [
            'PEPE/USDT:USDT',   # Meme coins - high volatility, lower fees
            'SHIB/USDT:USDT',
            'DOGE/USDT:USDT',
            'FLOKI/USDT:USDT',
            'WIF/USDT:USDT',
            'BONK/USDT:USDT',
            'SOL/USDT:USDT',    # Layer 1s - good liquidity
            'ADA/USDT:USDT',
            'AVAX/USDT:USDT',
            'MATIC/USDT:USDT',
            'DOT/USDT:USDT',
            'LINK/USDT:USDT',   # DeFi - moderate volatility
            'UNI/USDT:USDT',
            'AAVE/USDT:USDT',
            'BTC/USDT:USDT',    # Major coins - high liquidity, higher fees
            'ETH/USDT:USDT'
        ]
        
        # 🧠 EVALUATION CRITERIA: Multi-factor scoring system
        self.evaluation_weights = {
            'affordability': 0.25,      # Can we afford minimum order?
            'volatility': 0.20,         # Sufficient price movement for scalping
            'liquidity': 0.15,          # Order book depth and spread
            'trend_strength': 0.15,     # Clear directional movement
            'momentum': 0.10,           # Recent price momentum
            'fees': 0.10,              # Trading cost efficiency
            'leverage': 0.05           # Available leverage
        }
        
        # 💡 SMART FILTERS: Intelligent filtering criteria
        self.smart_filters = {
            'min_affordability_ratio': 0.4,    # Must be able to afford 40% of min order
            'max_spread_pct': 0.5,             # Maximum 0.5% spread
            'min_volume_24h': 1000000,         # Minimum $1M daily volume
            'min_volatility': 0.02,            # Minimum 2% volatility for opportunities
            'max_volatility': 0.15,            # Maximum 15% volatility for safety
            'min_liquidity_score': 0.3,        # Minimum liquidity threshold
            'preferred_leverage_min': 10.0     # Prefer symbols with at least 10x leverage
        }
        
        # 📊 SYMBOL CHARACTERISTICS: Known characteristics for quick lookup
        self.symbol_characteristics = {
            'PEPE/USDT:USDT': {'min_order': 8.0, 'typical_volatility': 0.08, 'leverage': 75},
            'SHIB/USDT:USDT': {'min_order': 15.0, 'typical_volatility': 0.06, 'leverage': 75},
            'DOGE/USDT:USDT': {'min_order': 26.96, 'typical_volatility': 0.05, 'leverage': 75},
            'SOL/USDT:USDT': {'min_order': 50.0, 'typical_volatility': 0.04, 'leverage': 50},
            'ADA/USDT:USDT': {'min_order': 30.0, 'typical_volatility': 0.04, 'leverage': 50},
            'BTC/USDT:USDT': {'min_order': 100.0, 'typical_volatility': 0.03, 'leverage': 100},
            'ETH/USDT:USDT': {'min_order': 80.0, 'typical_volatility': 0.035, 'leverage': 75}
        }
        
        logger.info(f"🎯 Intelligent Symbol Selector initialized for ${account_balance} balance")
        logger.info(f"🌐 Symbol universe: {len(self.symbol_universe)} symbols")
    
    def evaluate_symbol(self, symbol: str, market_data: Dict) -> SymbolMetrics:
        """
        Comprehensive evaluation of a single symbol
        
        Args:
            symbol: Symbol to evaluate
            market_data: Current market data for the symbol
            
        Returns:
            SymbolMetrics with comprehensive evaluation
        """
        try:
            # Extract market data
            price = market_data.get('price', 0.0)
            volume_24h = market_data.get('volume_24h', 0.0)
            spread_pct = market_data.get('spread_pct', 1.0)
            
            # Get symbol characteristics
            char = self.symbol_characteristics.get(symbol, {
                'min_order': 50.0,
                'typical_volatility': 0.05,
                'leverage': 50
            })
            
            # Calculate affordability score
            affordability_ratio = self.account_balance / char['min_order']
            affordability_score = min(1.0, affordability_ratio / 2.0)  # Full score if we can afford 2x minimum
            
            # Calculate volatility score (simulate if not provided)
            volatility = market_data.get('volatility', char['typical_volatility'])
            volatility_score = self._score_volatility(volatility)
            
            # Calculate liquidity score
            liquidity_score = self._calculate_liquidity_score(volume_24h, spread_pct)
            
            # Calculate trend and momentum (simulated for now)
            trend_strength = market_data.get('trend_strength', np.random.uniform(0.3, 0.8))
            momentum_score = market_data.get('momentum', np.random.uniform(0.4, 0.9))
            
            # Calculate fees score (lower fees = higher score)
            trading_fees = market_data.get('fees', 0.0006)  # Default 0.06%
            fees_score = max(0.0, 1.0 - (trading_fees / 0.001))  # Normalize against 0.1%
            
            # Calculate leverage score
            max_leverage = char['leverage']
            leverage_score = min(1.0, max_leverage / 100.0)  # Normalize against 100x
            
            # Calculate composite scores
            opportunity_score = (
                volatility_score * 0.4 +
                trend_strength * 0.3 +
                momentum_score * 0.3
            )
            
            risk_score = (
                (1.0 - volatility_score) * 0.4 +  # Lower volatility = lower risk
                liquidity_score * 0.3 +
                affordability_score * 0.3
            )
            
            # Calculate final weighted score
            final_score = (
                affordability_score * self.evaluation_weights['affordability'] +
                volatility_score * self.evaluation_weights['volatility'] +
                liquidity_score * self.evaluation_weights['liquidity'] +
                trend_strength * self.evaluation_weights['trend_strength'] +
                momentum_score * self.evaluation_weights['momentum'] +
                fees_score * self.evaluation_weights['fees'] +
                leverage_score * self.evaluation_weights['leverage']
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(symbol, affordability_score, volatility_score, 
                                               liquidity_score, opportunity_score, risk_score)
            
            return SymbolMetrics(
                symbol=symbol,
                price=price,
                volume_24h=volume_24h,
                volatility=volatility,
                spread_pct=spread_pct,
                liquidity_score=liquidity_score,
                trend_strength=trend_strength,
                momentum_score=momentum_score,
                min_order_value=char['min_order'],
                max_leverage=max_leverage,
                trading_fees=trading_fees,
                opportunity_score=opportunity_score,
                risk_score=risk_score,
                final_score=final_score,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error(f"Error evaluating symbol {symbol}: {e}")
            return self._create_fallback_metrics(symbol)
    
    def _score_volatility(self, volatility: float) -> float:
        """Score volatility for scalping opportunities"""
        # Optimal volatility range for scalping: 3-8%
        if volatility < 0.02:
            return volatility / 0.02  # Linear increase up to 2%
        elif volatility <= 0.08:
            return 1.0  # Perfect range
        else:
            return max(0.1, 1.0 - (volatility - 0.08) / 0.07)  # Decrease after 8%
    
    def _calculate_liquidity_score(self, volume_24h: float, spread_pct: float) -> float:
        """Calculate liquidity score based on volume and spread"""
        # Volume score (normalize against $10M)
        volume_score = min(1.0, volume_24h / 10000000)
        
        # Spread score (lower spread = higher score)
        spread_score = max(0.0, 1.0 - (spread_pct / 1.0))  # Normalize against 1%
        
        return (volume_score * 0.6 + spread_score * 0.4)
    
    def _generate_reasoning(self, symbol: str, affordability: float, volatility: float,
                          liquidity: float, opportunity: float, risk: float) -> str:
        """Generate human-readable reasoning for symbol selection"""
        reasons = []
        
        if affordability >= 0.8:
            reasons.append("✅ Highly affordable")
        elif affordability >= 0.4:
            reasons.append("⚠️ Moderately affordable")
        else:
            reasons.append("❌ Low affordability")
        
        if volatility >= 0.8:
            reasons.append("🚀 Excellent volatility")
        elif volatility >= 0.6:
            reasons.append("📈 Good volatility")
        else:
            reasons.append("📉 Low volatility")
        
        if liquidity >= 0.7:
            reasons.append("💧 High liquidity")
        elif liquidity >= 0.4:
            reasons.append("💧 Moderate liquidity")
        else:
            reasons.append("⚠️ Low liquidity")
        
        if opportunity >= 0.7:
            reasons.append("🎯 High opportunity")
        elif opportunity >= 0.5:
            reasons.append("🎯 Moderate opportunity")
        
        return " | ".join(reasons)
    
    def _create_fallback_metrics(self, symbol: str) -> SymbolMetrics:
        """Create fallback metrics for error cases"""
        return SymbolMetrics(
            symbol=symbol,
            price=0.0,
            volume_24h=0.0,
            volatility=0.0,
            spread_pct=1.0,
            liquidity_score=0.0,
            trend_strength=0.0,
            momentum_score=0.0,
            min_order_value=1000.0,  # High value to discourage selection
            max_leverage=1.0,
            trading_fees=0.001,
            opportunity_score=0.0,
            risk_score=0.0,
            final_score=0.0,
            reasoning="❌ Evaluation failed"
        )

    def select_best_symbols(self, live_data_manager, top_n: int = 5) -> List[SymbolMetrics]:
        """
        Select the best symbols for trading based on comprehensive evaluation

        Args:
            live_data_manager: Live data manager for fetching market data
            top_n: Number of top symbols to return

        Returns:
            List of top-ranked SymbolMetrics
        """
        try:
            logger.info(f"🎯 Evaluating {len(self.symbol_universe)} symbols for optimal trading opportunities")

            symbol_evaluations = []

            for symbol in self.symbol_universe:
                try:
                    # Get market data for symbol
                    market_data = self._fetch_symbol_market_data(live_data_manager, symbol)

                    # Evaluate symbol
                    metrics = self.evaluate_symbol(symbol, market_data)

                    # Apply smart filters
                    if self._passes_smart_filters(metrics):
                        symbol_evaluations.append(metrics)
                        logger.debug(f"✅ {symbol}: Score {metrics.final_score:.3f} - {metrics.reasoning}")
                    else:
                        logger.debug(f"❌ {symbol}: Filtered out - {metrics.reasoning}")

                except Exception as e:
                    logger.warning(f"⚠️ Error evaluating {symbol}: {e}")
                    continue

            # Sort by final score (descending)
            symbol_evaluations.sort(key=lambda x: x.final_score, reverse=True)

            # Return top N symbols
            top_symbols = symbol_evaluations[:top_n]

            logger.info(f"🏆 Top {len(top_symbols)} symbols selected:")
            for i, metrics in enumerate(top_symbols, 1):
                logger.info(f"  {i}. {metrics.symbol}: {metrics.final_score:.3f} - {metrics.reasoning}")

            return top_symbols

        except Exception as e:
            logger.error(f"Error selecting best symbols: {e}")
            return self._get_fallback_symbols()

    def _fetch_symbol_market_data(self, live_data_manager, symbol: str) -> Dict:
        """Fetch current market data for a symbol"""
        try:
            # Get current price and order book
            orderbook = live_data_manager.get_latest_orderbook(symbol)
            trades = live_data_manager.get_recent_trades(symbol, limit=10)
            candles = live_data_manager.get_chart_data(symbol, '1m', limit=20)

            if not orderbook or not orderbook.get('asks') or not orderbook.get('bids'):
                return self._get_simulated_market_data(symbol)

            # Calculate current metrics
            current_price = float(orderbook['asks'][0][0])
            spread = float(orderbook['asks'][0][0]) - float(orderbook['bids'][0][0])
            spread_pct = (spread / current_price) * 100

            # Calculate volume (simulate if not available)
            volume_24h = sum(float(trade.get('amount', 0)) * float(trade.get('price', current_price))
                           for trade in trades) * 144 if trades else 1000000  # Extrapolate to 24h

            # Calculate volatility from recent candles
            volatility = self._calculate_volatility(candles) if candles else 0.05

            return {
                'price': current_price,
                'spread_pct': spread_pct,
                'volume_24h': volume_24h,
                'volatility': volatility,
                'orderbook_depth': len(orderbook.get('bids', [])),
                'recent_trades': len(trades) if trades else 0
            }

        except Exception as e:
            logger.debug(f"Error fetching market data for {symbol}: {e}")
            return self._get_simulated_market_data(symbol)

    def _get_simulated_market_data(self, symbol: str) -> Dict:
        """Generate simulated market data for testing"""
        import random

        char = self.symbol_characteristics.get(symbol, {
            'min_order': 50.0,
            'typical_volatility': 0.05,
            'leverage': 50
        })

        # Simulate realistic market data
        base_price = {
            'BTC/USDT:USDT': 95000,
            'ETH/USDT:USDT': 3500,
            'SOL/USDT:USDT': 200,
            'DOGE/USDT:USDT': 0.19,
            'PEPE/USDT:USDT': 0.000008,
            'SHIB/USDT:USDT': 0.000025
        }.get(symbol, 1.0)

        return {
            'price': base_price * random.uniform(0.98, 1.02),
            'spread_pct': random.uniform(0.1, 0.8),
            'volume_24h': random.uniform(1000000, 50000000),
            'volatility': char['typical_volatility'] * random.uniform(0.8, 1.5),
            'orderbook_depth': random.randint(20, 100),
            'recent_trades': random.randint(5, 50)
        }

    def _calculate_volatility(self, candles: List) -> float:
        """Calculate volatility from price candles"""
        try:
            if len(candles) < 2:
                return 0.05

            # Calculate returns
            prices = [float(candle[4]) for candle in candles]  # Close prices
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]

            # Calculate standard deviation of returns
            if len(returns) > 1:
                mean_return = sum(returns) / len(returns)
                variance = sum((r - mean_return) ** 2 for r in returns) / (len(returns) - 1)
                volatility = (variance ** 0.5) * (24 ** 0.5)  # Annualized
                return min(volatility, 0.5)  # Cap at 50%

            return 0.05

        except Exception as e:
            logger.debug(f"Error calculating volatility: {e}")
            return 0.05

    def _passes_smart_filters(self, metrics: SymbolMetrics) -> bool:
        """Check if symbol passes smart filtering criteria"""
        try:
            # Affordability filter
            affordability_ratio = self.account_balance / metrics.min_order_value
            if affordability_ratio < self.smart_filters['min_affordability_ratio']:
                return False

            # Spread filter
            if metrics.spread_pct > self.smart_filters['max_spread_pct']:
                return False

            # Volume filter
            if metrics.volume_24h < self.smart_filters['min_volume_24h']:
                return False

            # Volatility filters
            if (metrics.volatility < self.smart_filters['min_volatility'] or
                metrics.volatility > self.smart_filters['max_volatility']):
                return False

            # Liquidity filter
            if metrics.liquidity_score < self.smart_filters['min_liquidity_score']:
                return False

            return True

        except Exception as e:
            logger.debug(f"Error applying smart filters: {e}")
            return False

    def _get_fallback_symbols(self) -> List[SymbolMetrics]:
        """Get fallback symbols if evaluation fails"""
        fallback_symbols = ['PEPE/USDT:USDT', 'SHIB/USDT:USDT', 'DOGE/USDT:USDT']

        return [self._create_fallback_metrics(symbol) for symbol in fallback_symbols]

    def get_recommended_symbol(self, live_data_manager) -> str:
        """Get the single best recommended symbol for trading"""
        try:
            best_symbols = self.select_best_symbols(live_data_manager, top_n=1)

            if best_symbols:
                recommended = best_symbols[0]
                logger.info(f"🎯 Recommended symbol: {recommended.symbol} (Score: {recommended.final_score:.3f})")
                logger.info(f"📊 Reasoning: {recommended.reasoning}")
                return recommended.symbol
            else:
                logger.warning("⚠️ No symbols passed filters, using PEPE/USDT:USDT as fallback")
                return 'PEPE/USDT:USDT'

        except Exception as e:
            logger.error(f"Error getting recommended symbol: {e}")
            return 'PEPE/USDT:USDT'
