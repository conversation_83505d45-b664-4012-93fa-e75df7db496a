{"emergency_mode": true, "reason": "insufficient_balance_for_doge_minimum", "timestamp": "2025-07-20T16:30:00Z", "account_status": {"available_balance": 19.51, "currency": "USDT"}, "original_configuration": {"symbol": "DOGE/USDT:USDT", "minimum_order_value": 26.96, "minimum_contracts": 100, "shortfall": 7.45}, "emergency_configuration": {"symbol": "PEPE/USDT:USDT", "minimum_order_value": 8.0, "minimum_contracts": 1000000, "viable": true, "safety_margin": 11.51}, "trading_parameters": {"position_size_pct": 80.0, "max_position_size": 15.61, "stop_loss_pct": 2.0, "take_profit_pct": 4.0, "max_daily_loss_pct": 5.0, "max_daily_loss_amount": 0.98, "max_daily_trades": 5, "leverage": 1.0, "monitoring_interval": 30}, "safety_systems": {"limit_orders_only": true, "emergency_stops_enabled": true, "risk_management_active": true, "ultra_conservative_mode": true}, "workflow_modification": {"original_workflow": "BTC/USDT:USDT → DOGE/USDT:USDT → ScalperGPT Auto", "modified_workflow": "BTC/USDT:USDT → PEPE/USDT:USDT → ScalperGPT Auto", "functionality_preserved": true, "autonomous_trading_enabled": true}, "system_status": {"configuration_updated": true, "symbol_scanner_updated": true, "gui_defaults_updated": true, "deployment_ready": true}, "next_steps": ["Validate modified workflow", "Test PEPE trading functionality", "Deploy autonomous trading", "Monitor performance", "Switch back to DOGE when balance > $30"]}