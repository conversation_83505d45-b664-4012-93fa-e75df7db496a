#!/usr/bin/env python3
"""
Emergency Autonomous Trading Launcher
Launches the GUI with PEPE emergency configuration and enables autonomous trading
"""

import sys
import os
import json
import time
import subprocess
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_emergency_launch_config():
    """Create emergency launch configuration for PEPE autonomous trading"""
    
    emergency_config = {
        "emergency_mode": True,
        "auto_enable_scalper": True,
        "default_symbol": "PEPE/USDT:USDT",
        "trading_parameters": {
            "position_size_pct": 80.0,
            "stop_loss_pct": 2.0,
            "take_profit_pct": 4.0,
            "max_daily_loss_pct": 5.0,
            "max_daily_trades": 5,
            "limit_orders_only": True
        },
        "safety_systems": {
            "emergency_stops_enabled": True,
            "risk_management_active": True,
            "ultra_conservative_mode": True
        },
        "scalper_thresholds": {
            "spread_quality": 7.0,
            "decision_quality": 8.0
        }
    }
    
    # Save emergency configuration
    with open('emergency_launch_config.json', 'w') as f:
        json.dump(emergency_config, f, indent=2)
    
    return emergency_config

def launch_emergency_autonomous_trading():
    """Launch emergency autonomous trading with PEPE configuration"""
    
    print("🚀 EMERGENCY AUTONOMOUS TRADING LAUNCHER")
    print("=" * 60)
    print(f"⏰ Launch Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create emergency configuration
    print(f"\n1️⃣ Creating Emergency Configuration...")
    emergency_config = create_emergency_launch_config()
    
    print(f"  📊 Emergency Symbol: {emergency_config['default_symbol']}")
    print(f"  💰 Position Size: {emergency_config['trading_parameters']['position_size_pct']}%")
    print(f"  🛡️ Stop Loss: {emergency_config['trading_parameters']['stop_loss_pct']}%")
    print(f"  🎯 Take Profit: {emergency_config['trading_parameters']['take_profit_pct']}%")
    print(f"  📋 Max Daily Trades: {emergency_config['trading_parameters']['max_daily_trades']}")
    print(f"  ✅ Emergency configuration created")
    
    # Validate system readiness
    print(f"\n2️⃣ Validating System Readiness...")
    
    try:
        # Check if emergency validation passed
        if os.path.exists('validate_emergency_config.py'):
            print(f"  🔍 Running emergency validation...")
            result = subprocess.run([sys.executable, 'validate_emergency_config.py'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"  ✅ Emergency validation PASSED")
            else:
                print(f"  ⚠️ Emergency validation had warnings, proceeding...")
        
        # Check key files exist
        required_files = [
            'launch_epinnox.py',
            'emergency_trading_config.json',
            'core/scalper_gpt.py'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"  ✅ {file_path} found")
            else:
                print(f"  ❌ {file_path} missing")
                return False
        
    except Exception as e:
        print(f"  ⚠️ Validation error: {e}, proceeding anyway...")
    
    # Launch instructions
    print(f"\n3️⃣ Autonomous Trading Launch Instructions:")
    print(f"=" * 60)
    
    print(f"""
🎯 EMERGENCY AUTONOMOUS TRADING WORKFLOW:

1. 🚀 Launch the GUI:
   python launch_epinnox.py --mode gui

2. 🔄 Symbol Switch (if needed):
   - Use dropdown to switch from BTC/USDT:USDT to PEPE/USDT:USDT
   - PEPE is now the default priority symbol

3. ✅ Enable Autonomous Trading:
   - Click the "ScalperGPT Auto" checkbox
   - System will start autonomous analysis and trading

4. 📊 Monitor Performance:
   - Watch real-time updates in the GUI
   - Monitor trade execution and PnL
   - Emergency stop available if needed

🚨 EMERGENCY CONFIGURATION ACTIVE:
   Symbol: PEPE/USDT:USDT
   Balance: $19.51
   Position Size: 80% ($15.61 max)
   Stop Loss: 2% | Take Profit: 4%
   Max Daily Loss: 5% ($0.98)
   Max Daily Trades: 5
   Order Type: LIMIT ORDERS ONLY

🛡️ SAFETY SYSTEMS ACTIVE:
   ✅ Emergency stops enabled
   ✅ Risk management active
   ✅ Ultra-conservative mode
   ✅ Quality thresholds enforced
   ✅ Position sizing validated

⚠️ IMPORTANT NOTES:
   - PEPE minimum order: $8 (viable with $19.51 balance)
   - System will automatically use PEPE as priority symbol
   - All trades will be LIMIT orders only
   - Emergency stops can be triggered manually or automatically
   - Monitor closely during initial deployment
""")
    
    # Offer to launch GUI automatically
    print(f"\n4️⃣ Ready to Launch GUI:")
    print(f"=" * 60)
    
    try:
        user_input = input("🚀 Launch GUI automatically? (y/n): ").lower().strip()
        
        if user_input in ['y', 'yes']:
            print(f"\n🚀 Launching Epinnox GUI with emergency configuration...")
            
            # Launch the GUI
            subprocess.Popen([sys.executable, 'launch_epinnox.py', '--mode', 'gui'])
            
            print(f"✅ GUI launched successfully!")
            print(f"📋 Follow the workflow above to enable autonomous trading")
            
            return True
        else:
            print(f"\n📋 Manual launch required:")
            print(f"   Run: python launch_epinnox.py --mode gui")
            return True
            
    except KeyboardInterrupt:
        print(f"\n⏹️ Launch cancelled by user")
        return False
    except Exception as e:
        print(f"\n❌ Launch error: {e}")
        return False

def show_monitoring_instructions():
    """Show monitoring instructions for autonomous trading"""
    
    print(f"\n📊 AUTONOMOUS TRADING MONITORING GUIDE:")
    print(f"=" * 60)
    
    monitoring_guide = """
🔍 WHAT TO MONITOR:

1. 📈 Market Analysis:
   - ScalperGPT analysis quality (≥ 7.0 spread, ≥ 8.0 decision)
   - PEPE price movements and volatility
   - Order book depth and liquidity

2. 🤖 Autonomous Decisions:
   - LONG/SHORT signals generated
   - Decision confidence levels
   - Quality threshold compliance

3. 💰 Trade Execution:
   - LIMIT order placements
   - Fill rates and execution times
   - Position sizes and leverage

4. 🛡️ Risk Management:
   - Stop loss triggers (2%)
   - Take profit targets (4%)
   - Daily loss tracking (max $0.98)
   - Position size compliance (max $15.61)

5. 🚨 Emergency Indicators:
   - Excessive losses
   - System errors or disconnections
   - Quality threshold violations
   - Unusual market conditions

⚠️ EMERGENCY STOP CONDITIONS:
   - Daily loss exceeds $0.98 (5% of balance)
   - 3+ consecutive losing trades
   - System errors or data feed issues
   - Manual intervention required

📞 EMERGENCY ACTIONS:
   1. Click "Emergency Stop" button in GUI
   2. Close all open positions
   3. Disable autonomous trading
   4. Review logs and performance
   5. Restart with adjusted parameters if needed
"""
    
    print(monitoring_guide)

if __name__ == "__main__":
    print("🚀 STARTING EMERGENCY AUTONOMOUS TRADING LAUNCHER")
    
    # Launch emergency autonomous trading
    launch_success = launch_emergency_autonomous_trading()
    
    if launch_success:
        # Show monitoring instructions
        show_monitoring_instructions()
        
        print(f"\n🎉 EMERGENCY AUTONOMOUS TRADING READY")
        print(f"✅ System configured for PEPE/USDT:USDT autonomous trading")
        print(f"🔄 Follow the workflow above to activate autonomous trading")
        
    else:
        print(f"\n❌ EMERGENCY LAUNCH FAILED")
        print(f"🔧 Manual intervention required")
    
    print(f"\n⏰ Launch completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
