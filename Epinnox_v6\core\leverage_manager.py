"""
Dynamic Leverage Manager for Epinnox v6

This module handles dynamic leverage fetching, risk-based leverage scaling,
and intelligent position sizing with leverage considerations.
"""

import logging
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class LeverageInfo:
    """Container for leverage information"""
    symbol: str
    max_leverage: float
    recommended_leverage: float
    leverage_reasoning: str
    risk_multiplier: float
    volatility_adjustment: float
    regime_adjustments: Dict[str, float] = None  # 🚨 ENHANCED: Store regime adjustments

@dataclass
class PositionSizeResult:
    """Container for position sizing results with leverage"""
    symbol: str
    position_units: float
    position_usd: float
    effective_leverage: float
    max_available_leverage: float
    recommended_leverage: float
    risk_per_trade_usd: float
    stop_loss_price: float
    take_profit_price: float
    leverage_reasoning: str
    risk_warnings: list

class DynamicLeverageManager:
    """
    Manages dynamic leverage fetching and intelligent position sizing
    """
    
    def __init__(self, base_balance: float = 50.0):
        """
        Initialize smart leverage manager with liquidation awareness

        Args:
            base_balance: Base account balance in USD
        """
        self.base_balance = base_balance

        # 🚀 SMART LEVERAGE: Maximize leverage while preventing overspending
        self.smart_leverage_config = {
            'use_max_available': True,  # Always try to use maximum available leverage
            'liquidation_buffer_pct': 15.0,  # Keep 15% buffer from liquidation
            'max_margin_usage_pct': 85.0,  # Use max 85% of available margin
            'emergency_reserve_pct': 10.0,  # Keep 10% emergency reserve
            'position_size_scaling': 'dynamic'  # Scale position size based on confidence
        }

        # 🧠 LIQUIDATION AWARENESS: Critical thresholds
        self.liquidation_thresholds = {
            'safe_zone': 0.30,      # 30%+ from liquidation = safe
            'warning_zone': 0.20,   # 20-30% from liquidation = warning
            'danger_zone': 0.10,    # 10-20% from liquidation = danger
            'critical_zone': 0.05   # <10% from liquidation = critical
        }

        # 💡 INTELLIGENT POSITION SIZING: Confidence-based scaling
        self.confidence_position_scaling = {
            'very_high': 1.0,     # 100% of calculated position for very high confidence (>90%)
            'high': 0.85,         # 85% of calculated position for high confidence (75-90%)
            'medium': 0.65,       # 65% of calculated position for medium confidence (60-75%)
            'low': 0.40,          # 40% of calculated position for low confidence (45-60%)
            'very_low': 0.20      # 20% of calculated position for very low confidence (<45%)
        }

        # 🎯 SYMBOL-SPECIFIC INTELLIGENCE: Enhanced risk assessment
        self.symbol_intelligence = {
            'BTC': {'volatility_factor': 0.8, 'liquidity_score': 1.0, 'max_leverage_usage': 0.9},
            'ETH': {'volatility_factor': 0.85, 'liquidity_score': 0.95, 'max_leverage_usage': 0.9},
            'DOGE': {'volatility_factor': 1.2, 'liquidity_score': 0.8, 'max_leverage_usage': 0.85},
            'PEPE': {'volatility_factor': 1.5, 'liquidity_score': 0.7, 'max_leverage_usage': 0.8},
            'SHIB': {'volatility_factor': 1.4, 'liquidity_score': 0.75, 'max_leverage_usage': 0.8},
            'SOL': {'volatility_factor': 1.1, 'liquidity_score': 0.85, 'max_leverage_usage': 0.85},
            'ADA': {'volatility_factor': 1.0, 'liquidity_score': 0.8, 'max_leverage_usage': 0.85}
        }

        logger.info(f"🚀 Smart Leverage Manager initialized with ${base_balance} balance")
        logger.info(f"🧠 Liquidation awareness enabled with {self.smart_leverage_config['liquidation_buffer_pct']}% buffer")
        logger.info(f"💡 Intelligent position sizing with confidence-based scaling")

    def calculate_liquidation_price(self, entry_price: float, leverage: float, side: str,
                                  maintenance_margin_rate: float = 0.005) -> float:
        """
        Calculate liquidation price for a position

        Args:
            entry_price: Entry price of the position
            leverage: Leverage used
            side: 'LONG' or 'SHORT'
            maintenance_margin_rate: Maintenance margin rate (default 0.5%)

        Returns:
            Liquidation price
        """
        try:
            if side.upper() == 'LONG':
                # For LONG: Liquidation Price = Entry Price × (1 - 1/Leverage + Maintenance Margin Rate)
                liquidation_price = entry_price * (1 - (1/leverage) + maintenance_margin_rate)
            else:  # SHORT
                # For SHORT: Liquidation Price = Entry Price × (1 + 1/Leverage - Maintenance Margin Rate)
                liquidation_price = entry_price * (1 + (1/leverage) - maintenance_margin_rate)

            return liquidation_price

        except Exception as e:
            logger.error(f"Error calculating liquidation price: {e}")
            return entry_price * 0.5 if side.upper() == 'LONG' else entry_price * 1.5

    def calculate_distance_to_liquidation(self, current_price: float, liquidation_price: float,
                                        side: str) -> dict:
        """
        Calculate distance to liquidation in percentage and absolute terms

        Args:
            current_price: Current market price
            liquidation_price: Calculated liquidation price
            side: 'LONG' or 'SHORT'

        Returns:
            Dictionary with liquidation distance metrics
        """
        try:
            if side.upper() == 'LONG':
                # For LONG positions, liquidation occurs when price drops
                distance_abs = current_price - liquidation_price
                distance_pct = (distance_abs / current_price) * 100
            else:  # SHORT
                # For SHORT positions, liquidation occurs when price rises
                distance_abs = liquidation_price - current_price
                distance_pct = (distance_abs / current_price) * 100

            # Determine risk zone
            if distance_pct >= self.liquidation_thresholds['safe_zone'] * 100:
                risk_zone = 'SAFE'
                risk_color = '🟢'
            elif distance_pct >= self.liquidation_thresholds['warning_zone'] * 100:
                risk_zone = 'WARNING'
                risk_color = '🟡'
            elif distance_pct >= self.liquidation_thresholds['danger_zone'] * 100:
                risk_zone = 'DANGER'
                risk_color = '🟠'
            else:
                risk_zone = 'CRITICAL'
                risk_color = '🔴'

            return {
                'distance_pct': distance_pct,
                'distance_abs': distance_abs,
                'liquidation_price': liquidation_price,
                'risk_zone': risk_zone,
                'risk_color': risk_color,
                'safe_to_trade': distance_pct >= self.liquidation_thresholds['warning_zone'] * 100
            }

        except Exception as e:
            logger.error(f"Error calculating liquidation distance: {e}")
            return {
                'distance_pct': 0.0,
                'distance_abs': 0.0,
                'liquidation_price': liquidation_price,
                'risk_zone': 'CRITICAL',
                'risk_color': '🔴',
                'safe_to_trade': False
            }

    def get_max_safe_leverage(self, symbol: str, confidence: float, market_volatility: float = 0.02) -> float:
        """
        Calculate maximum safe leverage based on symbol characteristics and market conditions

        Args:
            symbol: Trading symbol
            confidence: Signal confidence (0-1)
            market_volatility: Current market volatility

        Returns:
            Maximum safe leverage to use
        """
        try:
            # Get maximum available leverage for symbol
            base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
            max_available = self._get_default_leverage(symbol)

            # Get symbol intelligence
            symbol_info = self.symbol_intelligence.get(base_symbol, {
                'volatility_factor': 1.0,
                'liquidity_score': 0.8,
                'max_leverage_usage': 0.8
            })

            # Calculate safe leverage based on multiple factors
            volatility_adjustment = 1.0 / (1.0 + market_volatility * symbol_info['volatility_factor'])
            confidence_adjustment = confidence * 0.8 + 0.2  # Scale confidence to 0.2-1.0 range
            liquidity_adjustment = symbol_info['liquidity_score']
            max_usage_factor = symbol_info['max_leverage_usage']

            # Apply all adjustments
            safe_leverage = (max_available *
                           volatility_adjustment *
                           confidence_adjustment *
                           liquidity_adjustment *
                           max_usage_factor)

            # Ensure minimum viable leverage
            safe_leverage = max(safe_leverage, 2.0)

            # Cap at maximum available
            safe_leverage = min(safe_leverage, max_available)

            logger.info(f"🎯 Max safe leverage for {symbol}: {safe_leverage:.1f}x "
                       f"(max: {max_available}x, confidence: {confidence:.1%}, volatility: {market_volatility:.1%})")

            return safe_leverage

        except Exception as e:
            logger.error(f"Error calculating max safe leverage: {e}")
            return 5.0  # Conservative fallback
    
    def fetch_symbol_leverage(self, exchange, symbol: str) -> Optional[float]:
        """
        Fetch maximum leverage for a symbol from exchange
        
        Args:
            exchange: CCXT exchange instance
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            
        Returns:
            Maximum leverage or None if not found
        """
        try:
            # Load markets if not already loaded
            if not hasattr(exchange, 'markets') or not exchange.markets:
                exchange.load_markets()
            
            # Get market info for the symbol
            if symbol in exchange.markets:
                market = exchange.markets[symbol]
                
                # Try to get leverage from market info
                if 'limits' in market and 'leverage' in market['limits']:
                    max_leverage = market['limits']['leverage']['max']
                    if max_leverage:
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return float(max_leverage)
                
                # Try alternative fields
                if 'info' in market:
                    info = market['info']
                    # HTX specific fields
                    if 'leverage-ratio' in info:
                        max_leverage = float(info['leverage-ratio'])
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return max_leverage
                    
                    if 'maxLeverage' in info:
                        max_leverage = float(info['maxLeverage'])
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return max_leverage
            
            # Fallback: try to fetch from exchange-specific API
            if hasattr(exchange, 'fetch_trading_fees'):
                try:
                    fees = exchange.fetch_trading_fees([symbol])
                    if symbol in fees and 'leverage' in fees[symbol]:
                        max_leverage = fees[symbol]['leverage']
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return float(max_leverage)
                except Exception as e:
                    logger.debug(f"Could not fetch trading fees for leverage: {e}")
            
            # Default leverage based on symbol type
            default_leverage = self._get_default_leverage(symbol)
            logger.warning(f"Could not fetch leverage for {symbol}, using default: {default_leverage}x")
            return default_leverage
            
        except Exception as e:
            logger.error(f"Error fetching leverage for {symbol}: {e}")
            return self._get_default_leverage(symbol)
    
    def _get_default_leverage(self, symbol: str) -> float:
        """Get default leverage based on symbol type"""
        if ':USDT' in symbol or ':USD' in symbol:
            # Futures symbols typically have higher leverage
            if 'BTC' in symbol or 'ETH' in symbol:
                return 100.0  # Major coins
            elif 'DOGE' in symbol:
                return 75.0   # DOGE specific
            else:
                return 50.0   # Other altcoins
        else:
            # Spot symbols have lower leverage
            return 10.0
    
    def calculate_recommended_leverage(self,
                                     max_leverage: float,
                                     confidence: float,
                                     market_regime: str,
                                     symbol: str,
                                     volatility: float = None,
                                     regime_adjustments: Dict[str, float] = None) -> LeverageInfo:
        """
        Calculate recommended leverage based on multiple factors
        
        Args:
            max_leverage: Maximum available leverage
            confidence: Signal confidence (0-1)
            market_regime: Current market regime
            symbol: Trading symbol
            volatility: Current volatility (optional)
            
        Returns:
            LeverageInfo with recommended leverage and reasoning
        """
        try:
            reasoning_parts = []
            
            # 1. Confidence-based scaling
            if confidence >= 0.8:
                confidence_multiplier = self.confidence_leverage_scaling['high']
                reasoning_parts.append(f"High confidence ({confidence:.1%})")
            elif confidence >= 0.6:
                confidence_multiplier = self.confidence_leverage_scaling['medium']
                reasoning_parts.append(f"Medium confidence ({confidence:.1%})")
            else:
                confidence_multiplier = self.confidence_leverage_scaling['low']
                reasoning_parts.append(f"Low confidence ({confidence:.1%})")
            
            # 2. Market regime adjustment (🚨 ENHANCED: Use regime-based adjustments)
            if regime_adjustments and 'leverage_multiplier' in regime_adjustments:
                regime_multiplier = regime_adjustments['leverage_multiplier']
                reasoning_parts.append(f"Regime-based leverage: {regime_multiplier:.2f}x")
                logger.info(f"🎯 Applied regime-based leverage adjustment: {regime_multiplier:.2f}x for {market_regime}")
            else:
                regime_multiplier = self.regime_leverage_multipliers.get(market_regime, 0.5)
                reasoning_parts.append(f"Market regime: {market_regime}")
                logger.debug(f"Using static regime multiplier: {regime_multiplier:.2f}x for {market_regime}")
            
            # 3. Symbol-specific risk adjustment
            base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
            symbol_multiplier = self.symbol_risk_multipliers.get(base_symbol, 1.0)
            if symbol_multiplier != 1.0:
                reasoning_parts.append(f"Symbol risk: {base_symbol} ({symbol_multiplier:.1f}x)")
            
            # 4. Volatility adjustment
            volatility_multiplier = 1.0
            if volatility is not None:
                if volatility > 0.05:  # High volatility (>5%)
                    volatility_multiplier = 0.6
                    reasoning_parts.append(f"High volatility ({volatility:.1%})")
                elif volatility < 0.01:  # Low volatility (<1%)
                    volatility_multiplier = 1.1
                    reasoning_parts.append(f"Low volatility ({volatility:.1%})")
            
            # Calculate final leverage
            total_multiplier = (confidence_multiplier * 
                              regime_multiplier * 
                              symbol_multiplier * 
                              volatility_multiplier)
            
            recommended_leverage = min(max_leverage * total_multiplier, max_leverage)
            recommended_leverage = max(1.0, recommended_leverage)  # Minimum 1x leverage
            
            # Create reasoning string
            leverage_reasoning = f"Leverage scaling: {total_multiplier:.2f} = " + \
                               f"confidence({confidence_multiplier:.1f}) × " + \
                               f"regime({regime_multiplier:.1f}) × " + \
                               f"symbol({symbol_multiplier:.1f}) × " + \
                               f"volatility({volatility_multiplier:.1f}). " + \
                               f"Factors: {', '.join(reasoning_parts)}"
            
            return LeverageInfo(
                symbol=symbol,
                max_leverage=max_leverage,
                recommended_leverage=recommended_leverage,
                leverage_reasoning=leverage_reasoning,
                risk_multiplier=total_multiplier,
                volatility_adjustment=volatility_multiplier,
                regime_adjustments=regime_adjustments  # 🚨 ENHANCED: Include regime adjustments
            )
            
        except Exception as e:
            logger.error(f"Error calculating recommended leverage: {e}")
            # Conservative fallback
            return LeverageInfo(
                symbol=symbol,
                max_leverage=max_leverage,
                recommended_leverage=min(5.0, max_leverage),
                leverage_reasoning=f"Error in calculation, using conservative 5x leverage: {e}",
                risk_multiplier=0.1,
                volatility_adjustment=1.0
            )
    
    def calculate_position_size_with_leverage(self,
                                            leverage_info: LeverageInfo,
                                            current_price: float,
                                            signal_direction: str,
                                            stop_loss_pct: float = 0.05,
                                            take_profit_pct: float = 0.10,
                                            liquidity_score: float = 1.0) -> PositionSizeResult:
        """
        Calculate position size considering leverage and risk management
        
        Args:
            leverage_info: Leverage information
            current_price: Current market price
            signal_direction: LONG, SHORT, or WAIT
            stop_loss_pct: Stop loss percentage (default 5%)
            take_profit_pct: Take profit percentage (default 10%)
            liquidity_score: Market liquidity score (0-1)
            
        Returns:
            PositionSizeResult with detailed position information
        """
        try:
            warnings = []
            
            # Calculate maximum risk per trade (🚨 ENHANCED: Apply regime position size adjustment)
            base_risk_pct = self.max_risk_per_trade_pct

            # Apply regime-based position size adjustment if available
            if hasattr(leverage_info, 'regime_adjustments') and leverage_info.regime_adjustments:
                position_multiplier = leverage_info.regime_adjustments.get('position_size_multiplier', 1.0)
                adjusted_risk_pct = base_risk_pct * position_multiplier
                logger.info(f"🎯 Applied regime-based position size adjustment: {position_multiplier:.2f}x ({base_risk_pct:.1f}% -> {adjusted_risk_pct:.1f}%)")
            else:
                adjusted_risk_pct = base_risk_pct

            max_risk_usd = self.base_balance * (adjusted_risk_pct / 100)
            
            # Adjust for liquidity
            liquidity_adjustment = min(1.0, liquidity_score + 0.2)  # Minimum 20% of normal size
            if liquidity_score < 0.5:
                warnings.append(f"Low liquidity ({liquidity_score:.2f}) - position size reduced")
            
            # Calculate position size based on risk
            # Position size = (Risk Amount × Leverage) / (Stop Loss % × Price)
            effective_leverage = leverage_info.recommended_leverage * liquidity_adjustment
            
            if signal_direction == 'WAIT':
                # No position for WAIT signals
                position_units = 0.0
                position_usd = 0.0
                risk_per_trade = 0.0
                stop_loss_price = current_price
                take_profit_price = current_price
            else:
                # Calculate position size
                position_usd = (max_risk_usd * effective_leverage) / stop_loss_pct
                position_usd = min(position_usd, self.base_balance * effective_leverage)  # Don't exceed leveraged balance
                
                position_units = position_usd / current_price
                risk_per_trade = position_usd * stop_loss_pct / effective_leverage
                
                # Calculate stop loss and take profit prices
                if signal_direction == 'LONG':
                    stop_loss_price = current_price * (1 - stop_loss_pct)
                    take_profit_price = current_price * (1 + take_profit_pct)
                else:  # SHORT
                    stop_loss_price = current_price * (1 + stop_loss_pct)
                    take_profit_price = current_price * (1 - take_profit_pct)
            
            # Add warnings based on leverage usage
            leverage_usage_pct = (effective_leverage / leverage_info.max_leverage) * 100
            if leverage_usage_pct > 80:
                warnings.append(f"High leverage usage ({leverage_usage_pct:.1f}% of max)")
            elif leverage_usage_pct < 20:
                warnings.append(f"Conservative leverage usage ({leverage_usage_pct:.1f}% of max)")
            
            # Risk warnings
            risk_pct = (risk_per_trade / self.base_balance) * 100
            if risk_pct > self.max_risk_per_trade_pct:
                warnings.append(f"Risk exceeds limit ({risk_pct:.1f}% > {self.max_risk_per_trade_pct}%)")
            
            return PositionSizeResult(
                symbol=leverage_info.symbol,
                position_units=position_units,
                position_usd=position_usd,
                effective_leverage=effective_leverage,
                max_available_leverage=leverage_info.max_leverage,
                recommended_leverage=leverage_info.recommended_leverage,
                risk_per_trade_usd=risk_per_trade,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                leverage_reasoning=leverage_info.leverage_reasoning,
                risk_warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error calculating position size with leverage: {e}")
            # Return safe defaults
            return PositionSizeResult(
                symbol=leverage_info.symbol,
                position_units=0.0,
                position_usd=0.0,
                effective_leverage=1.0,
                max_available_leverage=leverage_info.max_leverage,
                recommended_leverage=1.0,
                risk_per_trade_usd=0.0,
                stop_loss_price=current_price,
                take_profit_price=current_price,
                leverage_reasoning=f"Error in calculation: {e}",
                risk_warnings=[f"Calculation error: {e}"]
            )
    
    def update_balance(self, new_balance: float):
        """Update the base balance"""
        self.base_balance = new_balance
        logger.info(f"Updated base balance to ${new_balance}")
    
    def get_leverage_summary(self, position_result: PositionSizeResult) -> Dict[str, Any]:
        """Get a summary of leverage information for logging/display"""
        return {
            'max_available_leverage': f"{position_result.max_available_leverage:.1f}x",
            'recommended_leverage': f"{position_result.recommended_leverage:.1f}x",
            'effective_leverage': f"{position_result.effective_leverage:.1f}x",
            'position_size_units': f"{position_result.position_units:.2f}",
            'position_size_usd': f"${position_result.position_usd:.2f}",
            'risk_per_trade': f"${position_result.risk_per_trade_usd:.2f}",
            'stop_loss_price': f"${position_result.stop_loss_price:.4f}",
            'take_profit_price': f"${position_result.take_profit_price:.4f}",
            'leverage_reasoning': position_result.leverage_reasoning,
            'risk_warnings': position_result.risk_warnings
        }
