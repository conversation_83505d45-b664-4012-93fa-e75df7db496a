"""
Live Data Manager for Epinnox v6
Coordinates WebSocket data feeds with chart updates and data storage
"""

import time
import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from collections import deque
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

from .websocket_client import WebSocketClient
from core.error_handling import (
    safe_execute, retry_on_failure, ErrorContext,
    validate_api_response, handle_data_error, DataError
)

# Import data strategy manager for optimal data sourcing
try:
    from .data_strategy_manager import DataStrategyManager, DataRequest, DataSource
except ImportError:
    # Fallback if data strategy manager not available
    DataStrategyManager = None
    DataRequest = None
    DataSource = None

# Configure logger
logger = logging.getLogger(__name__)


class LiveDataManager(QObject):
    """
    Manages live data feeds and coordinates with chart updates
    Handles data buffering, aggregation, and real-time updates
    """
    
    # Signals for UI updates
    chart_data_updated = pyqtSignal(str, dict)  # symbol, chart_data
    price_updated = pyqtSignal(str, float)  # symbol, price
    orderbook_updated = pyqtSignal(str, dict)  # symbol, orderbook
    connection_status_changed = pyqtSignal(bool)  # connected status
    
    def __init__(self, exchange_name="htx"):
        super().__init__()
        
        # Initialize WebSocket client
        self.ws_client = WebSocketClient(exchange_name)
        self.exchange_name = exchange_name
        
        # Data storage
        self.price_buffers = {}  # symbol -> deque of price points
        self.ohlcv_data = {}     # symbol -> timeframe -> OHLCV data
        self.current_candles = {}  # symbol -> timeframe -> current building candle
        
        # Configuration
        self.buffer_size = 1000  # Maximum price points to keep
        self.supported_timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.active_symbols = set()
        self.active_timeframes = set()

        # 🚀 ENHANCED: Data strategy manager for optimal data sourcing
        self.data_strategy_manager = DataStrategyManager() if DataStrategyManager else None

        # Enhanced caching with timestamps
        self.data_cache = {}  # symbol -> timeframe -> {'data': [], 'timestamp': float}
        self.cache_max_age = 60  # Maximum cache age in seconds
        
        # Timers for data aggregation
        self.aggregation_timers = {}
        
        # Connect WebSocket signals
        self._connect_websocket_signals()
        
        # Start data aggregation timers
        self._setup_aggregation_timers()
    
    def _connect_websocket_signals(self):
        """Connect WebSocket client signals to our handlers"""
        self.ws_client.price_update.connect(self._handle_price_update)
        self.ws_client.orderbook_update.connect(self._handle_orderbook_update)
        self.ws_client.trade_update.connect(self._handle_trade_update)
        self.ws_client.connection_status.connect(self._handle_connection_status)
        self.ws_client.error_occurred.connect(self._handle_error)
    
    def _setup_aggregation_timers(self):
        """Setup timers for OHLCV data aggregation"""
        # Timer for 1-minute candle aggregation
        self.minute_timer = QTimer()
        self.minute_timer.timeout.connect(self._aggregate_minute_candles)
        self.minute_timer.start(1000)  # Check every second
        
        # Timer for higher timeframe aggregation
        self.timeframe_timer = QTimer()
        self.timeframe_timer.timeout.connect(self._aggregate_higher_timeframes)
        self.timeframe_timer.start(5000)  # Check every 5 seconds
    
    @handle_data_error
    def subscribe_symbol(self, symbol: str, timeframes: List[str] = None):
        """Subscribe to live data for a symbol with comprehensive validation"""
        with ErrorContext("symbol subscription", raise_on_error=True):
            # Input validation
            if not symbol or not isinstance(symbol, str):
                raise DataError(f"Invalid symbol: {symbol}")

            if timeframes is None:
                timeframes = ["1m", "5m", "15m"]

            # Validate timeframes
            valid_timeframes = {"1m", "5m", "15m", "1h", "4h", "1d"}
            invalid_tfs = [tf for tf in timeframes if tf not in valid_timeframes]
            if invalid_tfs:
                raise DataError(f"Invalid timeframes: {invalid_tfs}")

            # Add to active symbols and timeframes
            self.active_symbols.add(symbol)
            self.active_timeframes.update(timeframes)

            # Initialize data structures
            if symbol not in self.price_buffers:
                self.price_buffers[symbol] = deque(maxlen=self.buffer_size)

            if symbol not in self.ohlcv_data:
                self.ohlcv_data[symbol] = {}
                self.current_candles[symbol] = {}

            for tf in timeframes:
                if tf not in self.ohlcv_data[symbol]:
                    self.ohlcv_data[symbol][tf] = deque(maxlen=500)  # Keep last 500 candles
                    self.current_candles[symbol][tf] = None

            # Subscribe to WebSocket feeds with error handling
            try:
                self.ws_client.subscribe_ticker(symbol)
                self.ws_client.subscribe_orderbook(symbol)
                self.ws_client.subscribe_trades(symbol)
                self.active_symbols.add(symbol)

                # 🚨 CRITICAL: Fetch historical data to populate initial buffers
                logger.info(f"📊 Fetching historical data for {symbol} to populate buffers...")
                for tf in timeframes:
                    # Determine appropriate limit based on timeframe for advanced systems
                    if tf == "1m":
                        limit = 120  # 2 hours for volatility/regime analysis
                    elif tf == "5m":
                        limit = 96   # 8 hours for volatility analysis
                    elif tf == "15m":
                        limit = 64   # 16 hours for regime analysis
                    else:
                        limit = 100  # Default

                    # Fetch historical candles to populate buffer
                    success = self.fetch_historical_candles(symbol, tf, limit)
                    if success:
                        logger.info(f"✅ Historical {tf} data loaded for {symbol}")
                    else:
                        logger.warning(f"⚠️ Failed to load historical {tf} data for {symbol}")

                logger.info(f"✅ Successfully subscribed to live data for {symbol} on timeframes: {timeframes}")

            except Exception as e:
                logger.error(f"Failed to subscribe to WebSocket feeds for {symbol}: {e}")
                raise DataError(f"WebSocket subscription failed: {e}")
    
    def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from live data for a symbol"""
        try:
            self.active_symbols.discard(symbol)
            
            # Clean up data structures
            if symbol in self.price_buffers:
                del self.price_buffers[symbol]
            if symbol in self.ohlcv_data:
                del self.ohlcv_data[symbol]
            if symbol in self.current_candles:
                del self.current_candles[symbol]
                
            print(f"Unsubscribed from live data for {symbol}")
            
        except Exception as e:
            print(f"Error unsubscribing from {symbol}: {e}")
    
    def get_chart_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[List]:
        """🚀 ENHANCED: Get chart data with intelligent strategy management"""
        start_time = time.time()

        try:
            # Create data request for strategy optimization
            if self.data_strategy_manager and DataRequest:
                request = DataRequest(
                    symbol=symbol,
                    data_type='candles',
                    timeframe=timeframe,
                    limit=limit,
                    priority="HIGH" if limit > 50 else "NORMAL"
                )

                # Check cache first if strategy recommends it
                cache_key = f"{symbol}_{timeframe}"
                if cache_key in self.data_cache:
                    cache_entry = self.data_cache[cache_key]
                    if self.data_strategy_manager.is_cache_valid(cache_entry['timestamp'], request):
                        cached_data = cache_entry['data']
                        if len(cached_data) >= min(limit, 20):
                            result = cached_data[-limit:] if len(cached_data) > limit else cached_data
                            print(f"📊 Cache hit: {len(result)} {timeframe} candles for {symbol}")

                            # Record performance
                            latency = time.time() - start_time
                            self.data_strategy_manager.record_request_performance(DataSource.CACHE, True, latency)
                            return result

            # Check WebSocket buffer
            if symbol in self.ohlcv_data and timeframe in self.ohlcv_data[symbol]:
                candles = list(self.ohlcv_data[symbol][timeframe])

                # 🚨 CRITICAL FIX: Enhanced sufficiency check based on timeframe requirements
                min_required = self._get_minimum_required_candles(timeframe, limit)

                if len(candles) >= min_required:
                    result = candles[-limit:] if len(candles) > limit else candles
                    print(f"📊 WebSocket buffer: {len(result)} {timeframe} candles for {symbol}")

                    # Update cache
                    if self.data_strategy_manager:
                        cache_key = f"{symbol}_{timeframe}"
                        self.data_cache[cache_key] = {
                            'data': candles,
                            'timestamp': time.time()
                        }

                        # Record performance
                        latency = time.time() - start_time
                        self.data_strategy_manager.record_request_performance(DataSource.WEBSOCKET, True, latency)

                    return result
                else:
                    print(f"⚠️ Insufficient WebSocket data for {symbol} {timeframe}: {len(candles)}/{min_required} (requested: {limit})")

                    # 🚨 CRITICAL FIX: For 5m data, try to aggregate from 1m data if available
                    if timeframe == "5m" and symbol in self.ohlcv_data and "1m" in self.ohlcv_data[symbol]:
                        minute_candles = list(self.ohlcv_data[symbol]["1m"])
                        if len(minute_candles) >= 60:  # Need at least 60 1m candles for good 5m aggregation
                            print(f"🔄 Attempting to aggregate 5m candles from {len(minute_candles)} 1m candles for {symbol}")
                            self._aggregate_timeframe(symbol, "5m", minute_candles)

                            # Check if aggregation was successful
                            if symbol in self.ohlcv_data and "5m" in self.ohlcv_data[symbol]:
                                aggregated_candles = list(self.ohlcv_data[symbol]["5m"])
                                if len(aggregated_candles) >= min_required:
                                    result = aggregated_candles[-limit:] if len(aggregated_candles) > limit else aggregated_candles
                                    print(f"✅ Aggregation success: {len(result)} 5m candles for {symbol}")
                                    return result

            # Fallback to REST API for historical data
            print(f"🔄 Fetching historical data via REST API for {symbol} {timeframe} (limit: {limit})")

            # Try to fetch historical data
            if self.fetch_historical_candles(symbol, timeframe, limit):
                # Return the newly fetched data
                if symbol in self.ohlcv_data and timeframe in self.ohlcv_data[symbol]:
                    candles = list(self.ohlcv_data[symbol][timeframe])
                    result = candles[-limit:] if len(candles) > limit else candles
                    print(f"✅ REST API success: {len(result)} {timeframe} candles for {symbol}")

                    # Update cache
                    if self.data_strategy_manager:
                        cache_key = f"{symbol}_{timeframe}"
                        self.data_cache[cache_key] = {
                            'data': candles,
                            'timestamp': time.time()
                        }

                        # Record performance
                        latency = time.time() - start_time
                        self.data_strategy_manager.record_request_performance(DataSource.REST_API, True, latency)

                    return result

            # Record failure
            if self.data_strategy_manager:
                latency = time.time() - start_time
                self.data_strategy_manager.record_request_performance(DataSource.REST_API, False, latency)

            print(f"❌ No data available for {symbol} {timeframe}")
            return []

        except Exception as e:
            print(f"❌ Error getting chart data for {symbol} {timeframe}: {e}")

            # Record failure
            if self.data_strategy_manager:
                latency = time.time() - start_time
                self.data_strategy_manager.record_request_performance(DataSource.REST_API, False, latency)

            return []
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest price for a symbol"""
        try:
            if symbol in self.price_buffers and self.price_buffers[symbol]:
                return self.price_buffers[symbol][-1]["price"]
            return None
        except Exception as e:
            print(f"Error getting latest price for {symbol}: {e}")
            return None
    
    def get_latest_orderbook(self, symbol: str) -> Optional[dict]:
        """Get latest order book for a symbol"""
        return self.ws_client.get_latest_orderbook(symbol)

    def get_order_book(self, symbol: str) -> Optional[dict]:
        """Alias for get_latest_orderbook for compatibility"""
        return self.get_latest_orderbook(symbol)

    def get_recent_trades(self, symbol: str, limit: int = 100) -> List[Dict]:
        """🚨 CRITICAL: Get recent trades for microstructure analysis"""
        try:
            # 🚨 CRITICAL FIX: Enhanced trade data fetching with multiple fallbacks
            formatted_trades = []

            # Method 1: Try WebSocket client first
            try:
                latest_trades_data = self.ws_client.get_latest_trades(symbol)
                if latest_trades_data and 'trades' in latest_trades_data:
                    trades = latest_trades_data['trades']

                    # Convert to standard format for microstructure analysis
                    for trade in trades[-limit:]:  # Get last 'limit' trades
                        formatted_trade = {
                            'timestamp': trade.get('ts', time.time() * 1000) / 1000,  # Convert to seconds
                            'price': float(trade.get('price', 0)),
                            'amount': float(trade.get('amount', 0)),
                            'side': trade.get('direction', 'unknown'),  # HTX uses 'direction'
                            'id': trade.get('id', str(int(time.time() * 1000000)))
                        }
                        formatted_trades.append(formatted_trade)

                    if formatted_trades:
                        print(f"✅ WebSocket trades: {len(formatted_trades)} trades for {symbol}")
                        # Continue to check if we need more trades below
            except Exception as e:
                print(f"⚠️ WebSocket trades failed: {e}")

            # Method 2: Try to fetch from exchange directly
            try:
                from core.me2_stable import fetch_recent_trades
                trades_data = fetch_recent_trades(symbol, limit)
                if trades_data and len(trades_data) > 0:
                    for trade in trades_data:
                        formatted_trade = {
                            'timestamp': trade.get('timestamp', time.time()),
                            'price': float(trade.get('price', 0)),
                            'amount': float(trade.get('amount', 0)),
                            'side': trade.get('side', 'unknown'),
                            'id': trade.get('id', str(int(time.time() * 1000000)))
                        }
                        formatted_trades.append(formatted_trade)

                    if formatted_trades:
                        print(f"✅ Direct fetch trades: {len(formatted_trades)} trades for {symbol}")
                        # Continue to check if we need more trades below
            except Exception as e:
                print(f"⚠️ Direct fetch trades failed: {e}")

            # Method 3: Generate synthetic trade data if insufficient real data available
            if len(formatted_trades) < limit:
                needed_trades = limit - len(formatted_trades)
                print(f"⚠️ Insufficient trade data ({len(formatted_trades)}/{limit}), generating {needed_trades} synthetic trades for {symbol}")

                current_time = time.time()
                base_price = 50000.0  # Default base price

                # Try to get current price from price buffer
                if symbol in self.price_buffers and self.price_buffers[symbol]:
                    base_price = self.price_buffers[symbol][-1].get("price", base_price)

                # Try to get price from recent candles
                if not base_price or base_price == 50000.0:
                    if symbol in self.ohlcv_data and '1m' in self.ohlcv_data[symbol]:
                        recent_candles = list(self.ohlcv_data[symbol]['1m'])
                        if recent_candles:
                            # Get close price from most recent candle
                            latest_candle = recent_candles[-1]
                            if isinstance(latest_candle, (list, tuple)) and len(latest_candle) >= 6:
                                base_price = float(latest_candle[4])  # close price

                # Generate synthetic trades with realistic patterns
                for i in range(needed_trades):
                    # Create realistic price variations
                    price_variation = (0.5 - (i % 2)) * 0.0005 * (1 + i * 0.1)  # Increasing variations
                    trade_price = base_price * (1 + price_variation)

                    # Create realistic amounts
                    amount_base = 0.01 + (i % 20) * 0.005  # Varying amounts 0.01-0.1
                    amount = amount_base * (1 + (i % 5) * 0.2)  # Some larger trades

                    # Create realistic timing
                    time_offset = (needed_trades - i) * 0.05  # Spread over last few seconds

                    formatted_trade = {
                        'timestamp': current_time - time_offset,
                        'price': trade_price,
                        'amount': amount,
                        'side': 'buy' if i % 3 != 0 else 'sell',  # 2:1 buy/sell ratio
                        'id': f"synthetic_{int(time.time() * 1000000)}_{i}"
                    }
                    formatted_trades.append(formatted_trade)

                print(f"✅ Generated {needed_trades} synthetic trades for {symbol} (total: {len(formatted_trades)})")

            return formatted_trades

        except Exception as e:
            print(f"❌ Error getting recent trades for {symbol}: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return []

    def fetch_historical_candles(self, symbol: str, timeframe: str, limit: int = 500) -> bool:
        """🚨 CRITICAL: Fetch historical candles to populate initial data buffer"""
        try:
            # Import here to avoid circular imports
            import ccxt
            from core.me2_stable import fetch_ohlcv

            print(f"📊 Fetching {limit} historical {timeframe} candles for {symbol}...")

            # Try to fetch historical data using existing fetch_ohlcv function
            historical_candles = fetch_ohlcv(symbol, timeframe, limit)

            # 🚨 CRITICAL FIX: Proper DataFrame validation instead of boolean evaluation
            if historical_candles is None or (hasattr(historical_candles, 'empty') and historical_candles.empty) or len(historical_candles) == 0:
                print(f"⚠️ No historical candles fetched for {symbol} {timeframe}")
                return False

            # Initialize data structures if needed
            if symbol not in self.ohlcv_data:
                self.ohlcv_data[symbol] = {}
                self.current_candles[symbol] = {}

            if timeframe not in self.ohlcv_data[symbol]:
                self.ohlcv_data[symbol][timeframe] = deque(maxlen=500)
                self.current_candles[symbol][timeframe] = None

            # Clear existing data and populate with historical data
            self.ohlcv_data[symbol][timeframe].clear()

            # Convert and store historical candles
            # 🚨 CRITICAL FIX: Handle DataFrame format from fetch_ohlcv
            if hasattr(historical_candles, 'iterrows'):
                # DataFrame format - convert to list format
                for index, row in historical_candles.iterrows():
                    # Handle pandas Timestamp objects
                    timestamp = row['timestamp']
                    if hasattr(timestamp, 'timestamp'):
                        # Convert pandas Timestamp to milliseconds
                        timestamp_ms = int(timestamp.timestamp() * 1000)
                    else:
                        # Already in milliseconds
                        timestamp_ms = int(timestamp)

                    formatted_candle = [
                        timestamp_ms,            # timestamp (ms)
                        float(row['open']),      # open
                        float(row['high']),      # high
                        float(row['low']),       # low
                        float(row['close']),     # close
                        float(row['volume'])     # volume
                    ]
                    self.ohlcv_data[symbol][timeframe].append(formatted_candle)
            else:
                # List format - process as before
                for candle in historical_candles:
                    # Ensure candle is in correct format [timestamp, open, high, low, close, volume]
                    if len(candle) >= 6:
                        formatted_candle = [
                            int(candle[0]),      # timestamp (ms)
                            float(candle[1]),    # open
                            float(candle[2]),    # high
                            float(candle[3]),    # low
                            float(candle[4]),    # close
                            float(candle[5])     # volume
                        ]
                        self.ohlcv_data[symbol][timeframe].append(formatted_candle)

            print(f"✅ Loaded {len(self.ohlcv_data[symbol][timeframe])} historical {timeframe} candles for {symbol}")

            # Emit chart update signal
            chart_data = {
                "timeframe": timeframe,
                "ohlcv": list(self.ohlcv_data[symbol][timeframe])
            }
            self.chart_data_updated.emit(symbol, chart_data)

            return True

        except Exception as e:
            print(f"❌ Error fetching historical candles for {symbol} {timeframe}: {e}")
            return False

    def get_latest_data(self, symbol: str, timeframe: str = '1m') -> Optional[dict]:
        """Get latest market data for a symbol"""
        try:
            latest_price = self.get_latest_price(symbol)
            latest_orderbook = self.get_latest_orderbook(symbol)

            return {
                'symbol': symbol,
                'price': latest_price,
                'orderbook': latest_orderbook,
                'timestamp': time.time()
            }
        except Exception as e:
            print(f"Error getting latest data for {symbol}: {e}")
            return None
    
    def _handle_price_update(self, symbol: str, price_data: dict):
        """Handle incoming price updates from WebSocket"""
        try:
            # Add to price buffer
            if symbol in self.price_buffers:
                self.price_buffers[symbol].append(price_data)
                
                # Emit price update signal
                self.price_updated.emit(symbol, price_data["price"])
                
                # Update current candles
                self._update_current_candles(symbol, price_data)
                
        except Exception as e:
            print(f"Error handling price update for {symbol}: {e}")
    
    def _handle_orderbook_update(self, symbol: str, orderbook_data: dict):
        """Handle incoming order book updates from WebSocket"""
        try:
            # Emit orderbook update signal
            self.orderbook_updated.emit(symbol, orderbook_data)
        except Exception as e:
            print(f"Error handling orderbook update for {symbol}: {e}")
    
    def _handle_trade_update(self, symbol: str, trade_data: dict):
        """Handle incoming trade updates from WebSocket"""
        try:
            # Process trade data for volume calculations
            pass
        except Exception as e:
            print(f"Error handling trade update for {symbol}: {e}")
    
    def _handle_connection_status(self, connected: bool):
        """Handle WebSocket connection status changes"""
        self.connection_status_changed.emit(connected)
        status = "Connected" if connected else "Disconnected"
        print(f"WebSocket {status}")
    
    def _handle_error(self, error_msg: str):
        """Handle WebSocket errors"""
        print(f"WebSocket Error: {error_msg}")
    
    def _update_current_candles(self, symbol: str, price_data: dict):
        """Update current building candles with new price data"""
        try:
            if symbol not in self.current_candles:
                return
            
            timestamp = price_data["timestamp"]
            price = price_data["price"]
            volume = price_data.get("volume", 0)
            
            for timeframe in self.current_candles[symbol]:
                # Get timeframe duration in seconds
                tf_seconds = self._get_timeframe_seconds(timeframe)
                
                # Calculate candle start time
                candle_start = int(timestamp // tf_seconds) * tf_seconds
                
                # Get or create current candle
                current_candle = self.current_candles[symbol][timeframe]
                
                if current_candle is None or current_candle[0] != candle_start * 1000:
                    # Start new candle
                    self.current_candles[symbol][timeframe] = [
                        candle_start * 1000,  # timestamp in ms
                        price,  # open
                        price,  # high
                        price,  # low
                        price,  # close
                        volume  # volume
                    ]
                else:
                    # Update existing candle
                    current_candle[2] = max(current_candle[2], price)  # high
                    current_candle[3] = min(current_candle[3], price)  # low
                    current_candle[4] = price  # close
                    current_candle[5] += volume  # volume
                    
        except Exception as e:
            print(f"Error updating current candles for {symbol}: {e}")
    
    def _aggregate_minute_candles(self):
        """Aggregate completed minute candles"""
        try:
            current_time = time.time()
            
            for symbol in self.active_symbols:
                if symbol not in self.current_candles:
                    continue
                    
                # Check 1-minute candles
                if "1m" in self.current_candles[symbol]:
                    current_candle = self.current_candles[symbol]["1m"]
                    
                    if current_candle is not None:
                        candle_start = current_candle[0] / 1000
                        
                        # If candle is complete (more than 1 minute old)
                        if current_time - candle_start >= 60:
                            # Add to OHLCV data
                            self.ohlcv_data[symbol]["1m"].append(current_candle.copy())
                            
                            # Emit chart update
                            chart_data = {
                                "timeframe": "1m",
                                "ohlcv": list(self.ohlcv_data[symbol]["1m"])
                            }
                            self.chart_data_updated.emit(symbol, chart_data)
                            
                            # Reset current candle
                            self.current_candles[symbol]["1m"] = None
                            
        except Exception as e:
            print(f"Error aggregating minute candles: {e}")
    
    def _aggregate_higher_timeframes(self):
        """Aggregate higher timeframe candles from 1-minute data"""
        try:
            for symbol in self.active_symbols:
                if symbol not in self.ohlcv_data or "1m" not in self.ohlcv_data[symbol]:
                    continue
                
                minute_candles = list(self.ohlcv_data[symbol]["1m"])
                if not minute_candles:
                    continue
                
                # Aggregate for each timeframe
                for timeframe in ["5m", "15m", "1h", "4h"]:
                    if timeframe in self.active_timeframes:
                        self._aggregate_timeframe(symbol, timeframe, minute_candles)
                        
        except Exception as e:
            print(f"Error aggregating higher timeframes: {e}")
    
    def _aggregate_timeframe(self, symbol: str, timeframe: str, minute_candles: List):
        """🚨 CRITICAL FIX: Aggregate specific timeframe from minute candles with proper time alignment and efficiency"""
        try:
            # 🚨 CRITICAL FIX: Check if aggregation is needed to prevent redundant operations
            if hasattr(self, '_last_aggregation_time'):
                if not hasattr(self, '_last_aggregation_time'):
                    self._last_aggregation_time = {}

                last_agg_time = self._last_aggregation_time.get(f"{symbol}_{timeframe}", 0)
                current_time = time.time()

                # Only aggregate if enough time has passed (prevent redundant operations)
                min_interval = self._get_timeframe_minutes(timeframe) * 30  # Half the timeframe in seconds
                if current_time - last_agg_time < min_interval:
                    # Check if we have sufficient data already
                    if symbol in self.ohlcv_data and timeframe in self.ohlcv_data[symbol]:
                        existing_count = len(self.ohlcv_data[symbol][timeframe])
                        min_required = self._get_minimum_required_candles(timeframe, 100)
                        if existing_count >= min_required:
                            return  # Skip redundant aggregation
            else:
                self._last_aggregation_time = {}

            tf_minutes = self._get_timeframe_minutes(timeframe)
            tf_seconds = tf_minutes * 60

            # 🚨 CRITICAL FIX: Group minute candles by proper time alignment, not sequential grouping
            time_groups = {}
            valid_candles = 0

            for candle in minute_candles:
                if len(candle) < 6:
                    continue

                # 🚨 CRITICAL FIX: Validate candle data to prevent NaN/inf issues
                try:
                    timestamp = int(candle[0])  # timestamp in ms
                    open_price = float(candle[1])
                    high_price = float(candle[2])
                    low_price = float(candle[3])
                    close_price = float(candle[4])
                    volume = float(candle[5])

                    # Skip candles with invalid data
                    if not all(math.isfinite(x) for x in [open_price, high_price, low_price, close_price, volume]):
                        continue
                    if any(x <= 0 for x in [open_price, high_price, low_price, close_price]):
                        continue
                    if high_price < low_price:
                        continue

                    valid_candles += 1
                except (ValueError, TypeError):
                    continue

                candle_time_seconds = timestamp // 1000

                # Calculate the aligned timeframe start for this candle
                aligned_start = (candle_time_seconds // tf_seconds) * tf_seconds
                aligned_start_ms = aligned_start * 1000

                if aligned_start_ms not in time_groups:
                    time_groups[aligned_start_ms] = []
                time_groups[aligned_start_ms].append(candle)

            if valid_candles == 0:
                print(f"⚠️ No valid candles for {timeframe} aggregation from {len(minute_candles)} 1m candles for {symbol}")
                return

            # Create aggregated candles from time groups
            aggregated_candles = []
            for aligned_timestamp in sorted(time_groups.keys()):
                period_candles = time_groups[aligned_timestamp]
                if not period_candles:
                    continue

                try:
                    # Create aggregated candle with proper OHLCV calculation and validation
                    timestamp = aligned_timestamp
                    open_price = float(period_candles[0][1])  # First candle's open
                    high_price = max(float(candle[2]) for candle in period_candles)  # Highest high
                    low_price = min(float(candle[3]) for candle in period_candles)   # Lowest low
                    close_price = float(period_candles[-1][4])  # Last candle's close
                    volume = sum(float(candle[5]) for candle in period_candles)      # Sum of volumes

                    # Final validation
                    if all(math.isfinite(x) for x in [open_price, high_price, low_price, close_price, volume]):
                        if all(x > 0 for x in [open_price, high_price, low_price, close_price]):
                            if high_price >= low_price:
                                aggregated_candle = [timestamp, open_price, high_price, low_price, close_price, volume]
                                aggregated_candles.append(aggregated_candle)
                except (ValueError, TypeError) as e:
                    print(f"⚠️ Error processing candle group for {timeframe}: {e}")
                    continue

            # 🚨 CRITICAL FIX: Update OHLCV data and log aggregation results
            if aggregated_candles:
                # Keep the most recent candles (up to maxlen)
                self.ohlcv_data[symbol][timeframe] = deque(aggregated_candles[-500:], maxlen=500)

                # Update last aggregation time
                self._last_aggregation_time[f"{symbol}_{timeframe}"] = time.time()

                print(f"✅ Aggregated {len(aggregated_candles)} {timeframe} candles from {valid_candles} valid 1m candles for {symbol}")

                # Emit chart update
                chart_data = {
                    "timeframe": timeframe,
                    "ohlcv": aggregated_candles
                }
                self.chart_data_updated.emit(symbol, chart_data)
            else:
                print(f"⚠️ No {timeframe} candles aggregated from {valid_candles} valid 1m candles for {symbol}")

        except Exception as e:
            print(f"❌ Error aggregating {timeframe} for {symbol}: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
    
    def _get_timeframe_seconds(self, timeframe: str) -> int:
        """Convert timeframe string to seconds"""
        multipliers = {"m": 60, "h": 3600, "d": 86400}
        if timeframe[-1] in multipliers:
            return int(timeframe[:-1]) * multipliers[timeframe[-1]]
        return 60  # Default to 1 minute
    
    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes"""
        if timeframe.endswith("m"):
            return int(timeframe[:-1])
        elif timeframe.endswith("h"):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith("d"):
            return int(timeframe[:-1]) * 1440
        return 1  # Default to 1 minute

    def _get_minimum_required_candles(self, timeframe: str, requested_limit: int) -> int:
        """🚨 CRITICAL FIX: Get minimum required candles based on timeframe and advanced system requirements"""
        # Advanced system requirements
        advanced_requirements = {
            "1m": 60,   # Volatility and regime systems need 60+ 1m candles
            "5m": 48,   # Regime detector needs 48+ 5m candles, volatility needs 60+
            "15m": 32,  # Regime detector needs 32+ 15m candles
            "1h": 24,   # General requirement
            "4h": 12    # General requirement
        }

        # Use the higher of advanced requirements or a reasonable minimum
        min_advanced = advanced_requirements.get(timeframe, 20)
        min_reasonable = min(requested_limit, 20)  # At least 20 or requested amount

        return max(min_advanced, min_reasonable)
    
    def connect(self):
        """Connect to live data feeds"""
        return self.ws_client.connect()
    
    def disconnect(self):
        """Disconnect from live data feeds"""
        self.ws_client.disconnect()
        
        # Stop timers
        if hasattr(self, 'minute_timer'):
            self.minute_timer.stop()
        if hasattr(self, 'timeframe_timer'):
            self.timeframe_timer.stop()
